import { z } from 'zod'

// User Schema
export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().optional(),
  color: z.string(),
  cursor: z.object({
    x: z.number(),
    y: z.number(),
  }).optional(),
})

export type User = z.infer<typeof UserSchema>

// Chat Message Schema
export const ChatMessageSchema = z.object({
  id: z.string(),
  userId: z.string(),
  userName: z.string(),
  content: z.string(),
  timestamp: z.number(),
  type: z.enum(['text', 'system']).default('text'),
})

export type ChatMessage = z.infer<typeof ChatMessageSchema>

// AI Message Schema
export const AIMessageSchema = z.object({
  id: z.string(),
  content: z.string(),
  timestamp: z.number(),
  isStreaming: z.boolean().default(false),
  canvasContext: z.string().optional(),
})

export type AIMessage = z.infer<typeof AIMessageSchema>

// WebSocket Message Schema
export const WSMessageSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('user-join'),
    user: UserSchema,
  }),
  z.object({
    type: z.literal('user-leave'),
    userId: z.string(),
  }),
  z.object({
    type: z.literal('cursor-update'),
    userId: z.string(),
    cursor: z.object({
      x: z.number(),
      y: z.number(),
    }),
  }),
  z.object({
    type: z.literal('chat-message'),
    message: ChatMessageSchema,
  }),
  z.object({
    type: z.literal('canvas-update'),
    data: z.any(), // Excalidraw data
  }),
  z.object({
    type: z.literal('room-state'),
    users: z.array(UserSchema),
    messages: z.array(ChatMessageSchema),
    canvasData: z.any().nullable(),
  }),
])

export type WSMessage = z.infer<typeof WSMessageSchema>

// Room Schema
export const RoomSchema = z.object({
  id: z.string(),
  users: z.map(z.string(), UserSchema),
  messages: z.array(ChatMessageSchema),
  canvasData: z.any().nullable(),
})

export type Room = z.infer<typeof RoomSchema>

// UI State Types
export interface UIState {
  isChatOpen: boolean
  isAIOpen: boolean
  isToolbarCollapsed: boolean
  isMenuOpen: boolean
  theme: 'light' | 'dark'
}

// Canvas Types
export interface CanvasState {
  elements: any[]
  appState: any
  files: any
}

// Media Types
export interface MediaSettings {
  audio: boolean
  video: boolean
  screen: boolean
}

export interface PeerConnection {
  userId: string
  connection: RTCPeerConnection
  stream?: MediaStream
}

// App Store State
export interface AppState {
  // User Management
  currentUser: User | null
  users: Map<string, User>
  
  // Room Management
  roomId: string | null
  isConnected: boolean
  
  // Chat
  chatMessages: ChatMessage[]
  aiMessages: AIMessage[]
  
  // Canvas
  canvasData: any
  
  // UI
  ui: UIState
  
  // Media
  mediaSettings: MediaSettings
  peerConnections: Map<string, PeerConnection>
  localStream: MediaStream | null
  
  // Actions
  setCurrentUser: (user: User) => void
  addUser: (user: User) => void
  removeUser: (userId: string) => void
  updateUserCursor: (userId: string, cursor: { x: number; y: number }) => void
  
  setRoomId: (roomId: string) => void
  setConnected: (connected: boolean) => void
  
  addChatMessage: (message: ChatMessage) => void
  addAIMessage: (message: AIMessage) => void
  updateAIMessage: (id: string, content: string, isStreaming?: boolean) => void
  
  updateCanvasData: (data: any) => void
  
  updateUI: (updates: Partial<UIState>) => void
  
  updateMediaSettings: (settings: Partial<MediaSettings>) => void
  addPeerConnection: (userId: string, connection: PeerConnection) => void
  removePeerConnection: (userId: string) => void
  setLocalStream: (stream: MediaStream | null) => void
}
