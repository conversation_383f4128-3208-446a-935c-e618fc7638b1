'use client'

import { useState, useRef, useEffect } from 'react'
import { useAppStore } from '@/stores/useAppStore'
import { useWebSocket } from '@/hooks/useWebSocket'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { ChatMessage } from '@/types'
import { formatTimestamp, generateUserId } from '@/lib/utils'
import { Send, X, MessageCircle } from 'lucide-react'

export function ChatPanel() {
  const [message, setMessage] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const {
    chatMessages,
    addChatMessage,
    currentUser,
    roomId,
    updateUI,
  } = useAppStore()

  const { sendMessage } = useWebSocket(roomId)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [chatMessages])

  useEffect(() => {
    // Focus input when panel opens
    inputRef.current?.focus()
  }, [])

  const handleSendMessage = () => {
    if (!message.trim() || !currentUser) return

    const chatMessage: ChatMessage = {
      id: generateUserId(),
      userId: currentUser.id,
      userName: currentUser.name,
      content: message.trim(),
      timestamp: Date.now(),
      type: 'text',
    }

    addChatMessage(chatMessage)
    
    if (sendMessage) {
      sendMessage({
        type: 'chat-message',
        message: chatMessage,
      })
    }

    setMessage('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="floating-panel h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          <h3 className="font-semibold">Chat</h3>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => updateUI({ isChatOpen: false })}
          className="h-8 w-8"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {chatMessages.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No messages yet</p>
            <p className="text-xs">Start a conversation!</p>
          </div>
        ) : (
          chatMessages.map((msg) => (
            <div
              key={msg.id}
              className={`flex flex-col space-y-1 ${
                msg.userId === currentUser?.id ? 'items-end' : 'items-start'
              }`}
            >
              <div
                className={`max-w-[80%] rounded-lg px-3 py-2 text-sm ${
                  msg.userId === currentUser?.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary text-secondary-foreground'
                }`}
              >
                {msg.userId !== currentUser?.id && (
                  <div className="text-xs font-medium mb-1 opacity-70">
                    {msg.userName}
                  </div>
                )}
                <div className="whitespace-pre-wrap break-words">
                  {msg.content}
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                {formatTimestamp(msg.timestamp)}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            ref={inputRef}
            placeholder="Type a message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!message.trim()}
            size="icon"
            className="shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
