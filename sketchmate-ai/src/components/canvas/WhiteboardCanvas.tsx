'use client'

import { useEffect, useRef, useState } from 'react'
import { Excalidraw, MainMenu, WelcomeScreen } from '@excalidraw/excalidraw'
import { useAppStore } from '@/stores/useAppStore'
import { useWebSocket } from '@/hooks/useWebSocket'
import { throttle } from '@/lib/utils'

export function WhiteboardCanvas() {
  const excalidrawRef = useRef<any>(null)
  const [excalidrawAPI, setExcalidrawAPI] = useState<any>(null)
  
  const {
    canvasData,
    updateCanvasData,
    roomId,
    ui,
  } = useAppStore()

  const { sendMessage } = useWebSocket(roomId)

  // Throttled canvas update function
  const throttledCanvasUpdate = useRef(
    throttle((elements: any, appState: any) => {
      const data = {
        elements,
        appState: {
          ...appState,
          // Remove sensitive data
          collaborators: undefined,
          isLoading: false,
        },
      }
      
      updateCanvasData(data)
      
      if (sendMessage) {
        sendMessage({
          type: 'canvas-update',
          data,
        })
      }
    }, 100)
  ).current

  const handleChange = (elements: any, appState: any) => {
    throttledCanvasUpdate(elements, appState)
  }

  // Apply theme changes
  useEffect(() => {
    if (excalidrawAPI) {
      excalidrawAPI.updateScene({
        appState: {
          theme: ui.theme,
        },
      })
    }
  }, [ui.theme, excalidrawAPI])

  // Load initial canvas data
  useEffect(() => {
    if (excalidrawAPI && canvasData) {
      excalidrawAPI.updateScene({
        elements: canvasData.elements || [],
        appState: {
          ...canvasData.appState,
          theme: ui.theme,
        },
      })
    }
  }, [canvasData, excalidrawAPI, ui.theme])

  return (
    <div className="h-full w-full">
      <Excalidraw
        ref={excalidrawRef}
        onChange={handleChange}
        excalidrawAPI={(api) => setExcalidrawAPI(api)}
        theme={ui.theme}
        initialData={{
          elements: canvasData?.elements || [],
          appState: {
            theme: ui.theme,
            viewBackgroundColor: ui.theme === 'dark' ? '#1e1e1e' : '#ffffff',
            gridSize: null,
            zenModeEnabled: false,
            viewModeEnabled: false,
          },
        }}
        UIOptions={{
          canvasActions: {
            loadScene: false,
            saveToActiveFile: false,
            export: {
              saveFileToDisk: true,
            },
            toggleTheme: false,
          },
        }}
      >
        <MainMenu>
          <MainMenu.DefaultItems.LoadScene />
          <MainMenu.DefaultItems.SaveAsImage />
          <MainMenu.DefaultItems.Help />
          <MainMenu.Separator />
          <MainMenu.DefaultItems.ClearCanvas />
          <MainMenu.Separator />
          <MainMenu.DefaultItems.ToggleTheme />
        </MainMenu>
        
        <WelcomeScreen>
          <WelcomeScreen.Hints.MenuHint />
          <WelcomeScreen.Hints.ToolbarHint />
          <WelcomeScreen.Center>
            <WelcomeScreen.Center.Logo />
            <WelcomeScreen.Center.Heading>
              Welcome to SketchMate AI!
            </WelcomeScreen.Center.Heading>
            <WelcomeScreen.Center.Menu>
              <WelcomeScreen.Center.MenuItemLoadScene />
              <WelcomeScreen.Center.MenuItemHelp />
            </WelcomeScreen.Center.Menu>
          </WelcomeScreen.Center>
        </WelcomeScreen>
      </Excalidraw>
    </div>
  )
}
