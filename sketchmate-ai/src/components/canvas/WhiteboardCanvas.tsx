'use client'

import dynamic from 'next/dynamic'

// Dynamically import the Excalidraw wrapper to avoid SSR issues
const ExcalidrawWrapper = dynamic(
  () => import('./ExcalidrawWrapper').then(mod => ({ default: mod.ExcalidrawWrapper })),
  {
    ssr: false,
    loading: () => (
      <div className="w-full h-full flex items-center justify-center bg-muted/20">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading whiteboard...</p>
        </div>
      </div>
    )
  }
)

export function WhiteboardCanvas() {
  return (
    <div className="w-full h-full">
      <ExcalidrawWrapper />
    </div>
  )
}
