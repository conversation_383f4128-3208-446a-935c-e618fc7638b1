'use client'

import { useAppStore } from '@/stores/useAppStore'
import { getInitials } from '@/lib/utils'

export function UserCursors() {
  const { users, currentUser } = useAppStore()

  return (
    <div className="absolute inset-0 pointer-events-none z-50">
      {Array.from(users.values())
        .filter(user => user.id !== currentUser?.id && user.cursor)
        .map(user => (
          <div
            key={user.id}
            className="absolute transition-all duration-100 ease-out"
            style={{
              left: user.cursor!.x,
              top: user.cursor!.y,
              transform: 'translate(-2px, -2px)',
            }}
          >
            {/* Cursor */}
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              className="drop-shadow-sm"
            >
              <path
                d="M5.65376 12.3673H5.46026L5.31717 12.4976L0.500002 16.8829L0.500002 1.19841L11.7841 12.3673H5.65376Z"
                fill={user.color}
                stroke="white"
                strokeWidth="1"
              />
            </svg>
            
            {/* User label */}
            <div
              className="absolute top-5 left-2 px-2 py-1 rounded text-xs font-medium text-white shadow-lg"
              style={{ backgroundColor: user.color }}
            >
              {user.name}
            </div>
          </div>
        ))}
    </div>
  )
}
