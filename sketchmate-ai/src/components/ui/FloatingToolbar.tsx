'use client'

import { useState } from 'react'
import { useAppStore } from '@/stores/useAppStore'
import { Button } from './Button'
import { 
  Menu, 
  Users, 
  Settings, 
  Download, 
  Upload,
  Maximize2,
  Minimize2,
  MoreHorizontal
} from 'lucide-react'

export function FloatingToolbar() {
  const { ui, updateUI, users } = useAppStore()
  const [isExpanded, setIsExpanded] = useState(false)

  const toggleExpanded = () => setIsExpanded(!isExpanded)

  return (
    <div className="floating-panel p-2">
      <div className="flex items-center gap-2">
        {/* Always visible: Menu toggle */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => updateUI({ isMenuOpen: !ui.isMenuOpen })}
          className="h-8 w-8"
        >
          <Menu className="h-4 w-4" />
        </Button>

        {/* Expandable section */}
        {isExpanded && (
          <div className="flex items-center gap-1 animate-fade-in">
            {/* Users indicator */}
            <div className="flex items-center gap-1 px-2 py-1 rounded bg-secondary text-secondary-foreground">
              <Users className="h-3 w-3" />
              <span className="text-xs font-medium">{users.size}</span>
            </div>

            {/* Toolbar actions */}
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Download className="h-4 w-4" />
            </Button>
            
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Upload className="h-4 w-4" />
            </Button>
            
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-8 w-8"
              onClick={() => updateUI({ isToolbarCollapsed: !ui.isToolbarCollapsed })}
            >
              {ui.isToolbarCollapsed ? (
                <Maximize2 className="h-4 w-4" />
              ) : (
                <Minimize2 className="h-4 w-4" />
              )}
            </Button>
            
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Expand/Collapse toggle */}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleExpanded}
          className="h-8 w-8"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
