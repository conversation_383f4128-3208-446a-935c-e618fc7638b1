'use client'

interface ConnectionStatusProps {
  isConnected: boolean
}

export function ConnectionStatus({ isConnected }: ConnectionStatusProps) {
  return (
    <div className="floating-panel px-3 py-2 flex items-center gap-2">
      <div
        className={`w-2 h-2 rounded-full ${
          isConnected 
            ? 'bg-green-500 animate-pulse-soft' 
            : 'bg-red-500'
        }`}
      />
      <span className="text-xs font-medium">
        {isConnected ? 'Connected' : 'Disconnected'}
      </span>
    </div>
  )
}
