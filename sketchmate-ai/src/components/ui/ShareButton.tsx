'use client'

import { useState } from 'react'
import { But<PERSON> } from './Button'
import { Share2, Co<PERSON>, Check } from 'lucide-react'
import { copyToClipboard } from '@/lib/utils'

interface ShareButtonProps {
  roomId: string
}

export function ShareButton({ roomId }: ShareButtonProps) {
  const [copied, setCopied] = useState(false)

  const handleShare = async () => {
    const url = `${window.location.origin}/room/${roomId}`
    const success = await copyToClipboard(url)
    
    if (success) {
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleShare}
      className="gap-2 bg-background/80 backdrop-blur-sm"
    >
      {copied ? (
        <>
          <Check className="h-4 w-4 text-green-600" />
          <span>Copied!</span>
        </>
      ) : (
        <>
          <Share2 className="h-4 w-4" />
          <span>Share</span>
        </>
      )}
    </Button>
  )
}
