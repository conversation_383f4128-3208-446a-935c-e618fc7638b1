'use client'

import { useState, useRef, useEffect } from 'react'
import { useAppStore } from '@/stores/useAppStore'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { AIMessage } from '@/types'
import { formatTimestamp, generateUserId } from '@/lib/utils'
import { Send, X, Bot, Sparkles } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import 'katex/dist/katex.min.css'

export function AIPanel() {
  const [prompt, setPrompt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const {
    aiMessages,
    addAIMessage,
    updateAIMessage,
    canvasData,
    updateUI,
  } = useAppStore()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [aiMessages])

  useEffect(() => {
    // Focus input when panel opens
    inputRef.current?.focus()
  }, [])

  const handleSendPrompt = async () => {
    if (!prompt.trim() || isLoading) return

    const userPrompt = prompt.trim()
    setPrompt('')
    setIsLoading(true)

    // Add user message
    const userMessage: AIMessage = {
      id: generateUserId(),
      content: `**You:** ${userPrompt}`,
      timestamp: Date.now(),
      isStreaming: false,
    }
    addAIMessage(userMessage)

    // Add AI response placeholder
    const aiMessageId = generateUserId()
    const aiMessage: AIMessage = {
      id: aiMessageId,
      content: '',
      timestamp: Date.now(),
      isStreaming: true,
      canvasContext: canvasData ? JSON.stringify(canvasData) : undefined,
    }
    addAIMessage(aiMessage)

    try {
      // Call AI API
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: userPrompt,
          canvasContext: canvasData,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response stream')
      }

      let accumulatedContent = ''
      
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break
        
        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.content) {
                accumulatedContent += data.content
                updateAIMessage(aiMessageId, accumulatedContent, true)
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }

      // Mark as complete
      updateAIMessage(aiMessageId, accumulatedContent, false)
      
    } catch (error) {
      console.error('AI request failed:', error)
      updateAIMessage(
        aiMessageId,
        '❌ Sorry, I encountered an error. Please try again.',
        false
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendPrompt()
    }
  }

  return (
    <div className="floating-panel h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-blue-600" />
          <h3 className="font-semibold">AI Assistant</h3>
          <Sparkles className="h-4 w-4 text-yellow-500" />
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => updateUI({ isAIOpen: false })}
          className="h-8 w-8"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {aiMessages.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <Bot className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">AI Assistant Ready</p>
            <p className="text-xs">Ask me about your drawing or anything else!</p>
          </div>
        ) : (
          aiMessages.map((msg) => (
            <div key={msg.id} className="space-y-2">
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <ReactMarkdown
                  components={{
                    code: ({ node, inline, className, children, ...props }) => {
                      if (inline) {
                        return (
                          <code className="bg-secondary px-1 py-0.5 rounded text-sm" {...props}>
                            {children}
                          </code>
                        )
                      }
                      return (
                        <pre className="bg-secondary p-3 rounded-lg overflow-x-auto">
                          <code {...props}>{children}</code>
                        </pre>
                      )
                    },
                  }}
                >
                  {msg.content}
                </ReactMarkdown>
              </div>
              
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{formatTimestamp(msg.timestamp)}</span>
                {msg.isStreaming && (
                  <div className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                    <span>Thinking...</span>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            ref={inputRef}
            placeholder="Ask AI about your drawing..."
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendPrompt}
            disabled={!prompt.trim() || isLoading}
            size="icon"
            className="shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
