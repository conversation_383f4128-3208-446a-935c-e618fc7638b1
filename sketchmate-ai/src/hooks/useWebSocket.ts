import { useEffect, useRef, useCallback } from 'react'
import { useAppStore } from '@/stores/useAppStore'
import { WSMessage, WSMessageSchema } from '@/types'

export function useWebSocket(roomId: string | null) {
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  const {
    setConnected,
    addUser,
    removeUser,
    updateUserCursor,
    addChatMessage,
    updateCanvasData,
    currentUser,
  } = useAppStore()

  const connect = useCallback(() => {
    if (!roomId || wsRef.current?.readyState === WebSocket.OPEN) return

    try {
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'
      const ws = new WebSocket(`${wsUrl}?room=${roomId}`)
      wsRef.current = ws

      ws.onopen = () => {
        console.log('WebSocket connected')
        setConnected(true)
        reconnectAttempts.current = 0

        // Join room with current user
        if (currentUser) {
          sendMessage({
            type: 'user-join',
            user: currentUser,
          })
        }
      }

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          const message = WSMessageSchema.parse(data)
          handleMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setConnected(false)
        wsRef.current = null

        // Attempt to reconnect if not a manual close
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000)
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++
            connect()
          }, delay)
        }
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
    }
  }, [roomId, currentUser, setConnected])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect')
      wsRef.current = null
    }
    setConnected(false)
  }, [setConnected])

  const sendMessage = useCallback((message: WSMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket not connected, message not sent:', message)
    }
  }, [])

  const handleMessage = useCallback((message: WSMessage) => {
    switch (message.type) {
      case 'user-join':
        addUser(message.user)
        break

      case 'user-leave':
        removeUser(message.userId)
        break

      case 'cursor-update':
        updateUserCursor(message.userId, message.cursor)
        break

      case 'chat-message':
        addChatMessage(message.message)
        break

      case 'canvas-update':
        updateCanvasData(message.data)
        break

      case 'room-state':
        // Handle initial room state
        message.users.forEach(user => addUser(user))
        message.messages.forEach(msg => addChatMessage(msg))
        if (message.canvasData) {
          updateCanvasData(message.canvasData)
        }
        break

      default:
        console.warn('Unknown message type:', message)
    }
  }, [addUser, removeUser, updateUserCursor, addChatMessage, updateCanvasData])

  // Connect when roomId changes
  useEffect(() => {
    if (roomId) {
      connect()
    } else {
      disconnect()
    }

    return () => {
      disconnect()
    }
  }, [roomId, connect, disconnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    sendMessage,
    isConnected: wsRef.current?.readyState === WebSocket.OPEN,
    connect,
    disconnect,
  }
}
