import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'SketchMate AI - Collaborative Whiteboard',
  description: 'AI-powered collaborative whiteboard with real-time drawing, chat, and seamless sharing',
  keywords: ['whiteboard', 'collaboration', 'AI', 'drawing', 'real-time'],
  authors: [{ name: 'SketchMate Team' }],
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#ffffff" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <div className="h-screen w-screen overflow-hidden bg-background">
          {children}
        </div>
      </body>
    </html>
  )
}
