import { GoogleGenerativeAI } from '@google/generative-ai'
import { NextRequest, NextResponse } from 'next/server'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

export async function POST(request: NextRequest) {
  try {
    const { prompt, canvasContext } = await request.json()

    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 })
    }

    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      )
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' })

    // Prepare the context
    let contextualPrompt = prompt
    
    if (canvasContext) {
      try {
        const canvas = JSON.parse(canvasContext)
        const elementCount = canvas.elements?.length || 0
        const hasElements = elementCount > 0
        
        contextualPrompt = `
Context: I'm working on a collaborative whiteboard with ${elementCount} elements drawn on the canvas.
${hasElements ? 'The canvas contains drawings, shapes, text, and other elements.' : 'The canvas is currently empty.'}

User question: ${prompt}

Please provide a helpful response. If the question is about the drawing, consider the canvas context. 
Use markdown formatting for better readability. Keep responses concise but informative.
`
      } catch (e) {
        // If canvas context parsing fails, just use the original prompt
        console.warn('Failed to parse canvas context:', e)
      }
    }

    // Create a readable stream for streaming response
    const encoder = new TextEncoder()
    
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const result = await model.generateContentStream(contextualPrompt)
          
          for await (const chunk of result.stream) {
            const chunkText = chunk.text()
            if (chunkText) {
              const data = JSON.stringify({ content: chunkText })
              controller.enqueue(encoder.encode(`data: ${data}\n\n`))
            }
          }
          
          controller.close()
        } catch (error) {
          console.error('Gemini API error:', error)
          const errorData = JSON.stringify({ 
            content: '❌ Sorry, I encountered an error processing your request.' 
          })
          controller.enqueue(encoder.encode(`data: ${errorData}\n\n`))
          controller.close()
        }
      },
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('API route error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
