'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAppStore } from '@/stores/useAppStore'
import { useWebSocket } from '@/hooks/useWebSocket'
import { WhiteboardCanvas } from '@/components/canvas/WhiteboardCanvas'
import { FloatingToolbar } from '@/components/ui/FloatingToolbar'
import { ChatPanel } from '@/components/chat/ChatPanel'
import { AIPanel } from '@/components/ai/AIPanel'
import { UserCursors } from '@/components/canvas/UserCursors'
import { ConnectionStatus } from '@/components/ui/ConnectionStatus'
import { ShareButton } from '@/components/ui/ShareButton'

export default function RoomPage() {
  const params = useParams()
  const router = useRouter()
  const roomId = params.roomId as string
  
  const {
    currentUser,
    setRoomId,
    ui,
    updateUI,
  } = useAppStore()

  const { isConnected } = useWebSocket(roomId)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!currentUser) {
      router.push('/')
      return
    }

    setRoomId(roomId)
    setIsLoading(false)
  }, [currentUser, roomId, router, setRoomId])

  if (isLoading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading whiteboard...</p>
        </div>
      </div>
    )
  }

  if (!currentUser) {
    return null
  }

  return (
    <div className="h-screen w-screen relative overflow-hidden bg-background">
      {/* Main Canvas */}
      <WhiteboardCanvas />
      
      {/* User Cursors */}
      <UserCursors />
      
      {/* Floating UI Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Top Bar */}
        <div className="absolute top-4 left-4 right-4 flex items-center justify-between pointer-events-auto">
          <div className="flex items-center gap-3">
            <ConnectionStatus isConnected={isConnected} />
            <div className="floating-panel px-3 py-2">
              <span className="text-sm font-medium">Room: {roomId}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <ShareButton roomId={roomId} />
            <FloatingToolbar />
          </div>
        </div>

        {/* Chat Panel */}
        {ui.isChatOpen && (
          <div className="absolute top-16 right-4 bottom-4 w-80 pointer-events-auto">
            <ChatPanel />
          </div>
        )}

        {/* AI Panel */}
        {ui.isAIOpen && (
          <div className="absolute top-16 right-4 bottom-4 w-80 pointer-events-auto">
            <AIPanel />
          </div>
        )}

        {/* Bottom Controls */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 pointer-events-auto">
          <div className="floating-panel px-4 py-2 flex items-center gap-3">
            <button
              onClick={() => updateUI({ isChatOpen: !ui.isChatOpen, isAIOpen: false })}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                ui.isChatOpen 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              💬 Chat
            </button>
            
            <button
              onClick={() => updateUI({ isAIOpen: !ui.isAIOpen, isChatOpen: false })}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                ui.isAIOpen 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              🤖 AI Assistant
            </button>
            
            <div className="w-px h-4 bg-border"></div>
            
            <button
              onClick={() => updateUI({ theme: ui.theme === 'light' ? 'dark' : 'light' })}
              className="px-3 py-1 rounded text-sm hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              {ui.theme === 'light' ? '🌙' : '☀️'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
