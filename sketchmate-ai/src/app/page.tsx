'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAppStore } from '@/stores/useAppStore'
import { User } from '@/types'
import { generateUserId, generateUserColor } from '@/lib/utils'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'

export default function Home() {
  const router = useRouter()
  const { setCurrentUser } = useAppStore()
  const [userName, setUserName] = useState('')
  const [roomId, setRoomId] = useState('')

  const createRoom = () => {
    const newRoomId = Math.random().toString(36).substring(2, 8)
    joinRoom(newRoomId)
  }

  const joinRoom = (targetRoomId?: string) => {
    const finalRoomId = targetRoomId || roomId
    if (!finalRoomId || !userName.trim()) return

    const user: User = {
      id: generateUserId(),
      name: userName.trim(),
      color: generateUserColor(),
    }

    setCurrentUser(user)
    router.push(`/room/${finalRoomId}`)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      joinRoom()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md p-8 space-y-6 bg-white/80 backdrop-blur-sm border-0 shadow-xl">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            SketchMate AI
          </h1>
          <p className="text-muted-foreground">
            Collaborative whiteboard with AI assistance
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="userName" className="text-sm font-medium">
              Your Name
            </label>
            <Input
              id="userName"
              placeholder="Enter your name"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="roomId" className="text-sm font-medium">
              Room ID (optional)
            </label>
            <Input
              id="roomId"
              placeholder="Enter room ID to join"
              value={roomId}
              onChange={(e) => setRoomId(e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full"
            />
          </div>

          <div className="space-y-3 pt-2">
            <Button
              onClick={createRoom}
              disabled={!userName.trim()}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              Create New Room
            </Button>
            
            <Button
              onClick={() => joinRoom()}
              disabled={!userName.trim() || !roomId.trim()}
              variant="outline"
              className="w-full"
            >
              Join Room
            </Button>
          </div>
        </div>

        <div className="text-center text-xs text-muted-foreground">
          <p>✨ Real-time collaboration • 🤖 AI assistance • 🎨 Infinite canvas</p>
        </div>
      </Card>
    </div>
  )
}
