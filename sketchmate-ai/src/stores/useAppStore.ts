import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { AppState, User, ChatMessage, AIMessage, UIState, MediaSettings, PeerConnection } from '@/types'

const initialUIState: UIState = {
  isChatOpen: false,
  isAIOpen: false,
  isToolbarCollapsed: false,
  isMenuOpen: false,
  theme: 'light',
}

const initialMediaSettings: MediaSettings = {
  audio: false,
  video: false,
  screen: false,
}

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // Initial State
      currentUser: null,
      users: new Map(),
      roomId: null,
      isConnected: false,
      chatMessages: [],
      aiMessages: [],
      canvasData: null,
      ui: initialUIState,
      mediaSettings: initialMediaSettings,
      peerConnections: new Map(),
      localStream: null,

      // User Actions
      setCurrentUser: (user: User) => {
        set({ currentUser: user }, false, 'setCurrentUser')
      },

      addUser: (user: User) => {
        set((state) => {
          const newUsers = new Map(state.users)
          newUsers.set(user.id, user)
          return { users: newUsers }
        }, false, 'addUser')
      },

      removeUser: (userId: string) => {
        set((state) => {
          const newUsers = new Map(state.users)
          newUsers.delete(userId)
          return { users: newUsers }
        }, false, 'removeUser')
      },

      updateUserCursor: (userId: string, cursor: { x: number; y: number }) => {
        set((state) => {
          const newUsers = new Map(state.users)
          const user = newUsers.get(userId)
          if (user) {
            newUsers.set(userId, { ...user, cursor })
          }
          return { users: newUsers }
        }, false, 'updateUserCursor')
      },

      // Room Actions
      setRoomId: (roomId: string) => {
        set({ roomId }, false, 'setRoomId')
      },

      setConnected: (connected: boolean) => {
        set({ isConnected: connected }, false, 'setConnected')
      },

      // Chat Actions
      addChatMessage: (message: ChatMessage) => {
        set((state) => ({
          chatMessages: [...state.chatMessages, message]
        }), false, 'addChatMessage')
      },

      addAIMessage: (message: AIMessage) => {
        set((state) => ({
          aiMessages: [...state.aiMessages, message]
        }), false, 'addAIMessage')
      },

      updateAIMessage: (id: string, content: string, isStreaming = false) => {
        set((state) => ({
          aiMessages: state.aiMessages.map(msg =>
            msg.id === id ? { ...msg, content, isStreaming } : msg
          )
        }), false, 'updateAIMessage')
      },

      // Canvas Actions
      updateCanvasData: (data: any) => {
        set({ canvasData: data }, false, 'updateCanvasData')
      },

      // UI Actions
      updateUI: (updates: Partial<UIState>) => {
        set((state) => ({
          ui: { ...state.ui, ...updates }
        }), false, 'updateUI')
      },

      // Media Actions
      updateMediaSettings: (settings: Partial<MediaSettings>) => {
        set((state) => ({
          mediaSettings: { ...state.mediaSettings, ...settings }
        }), false, 'updateMediaSettings')
      },

      addPeerConnection: (userId: string, connection: PeerConnection) => {
        set((state) => {
          const newConnections = new Map(state.peerConnections)
          newConnections.set(userId, connection)
          return { peerConnections: newConnections }
        }, false, 'addPeerConnection')
      },

      removePeerConnection: (userId: string) => {
        set((state) => {
          const newConnections = new Map(state.peerConnections)
          newConnections.delete(userId)
          return { peerConnections: newConnections }
        }, false, 'removePeerConnection')
      },

      setLocalStream: (stream: MediaStream | null) => {
        set({ localStream: stream }, false, 'setLocalStream')
      },
    }),
    {
      name: 'sketchmate-store',
    }
  )
)

// Selectors for optimized re-renders
export const useCurrentUser = () => useAppStore((state) => state.currentUser)
export const useUsers = () => useAppStore((state) => state.users)
export const useRoomId = () => useAppStore((state) => state.roomId)
export const useIsConnected = () => useAppStore((state) => state.isConnected)
export const useChatMessages = () => useAppStore((state) => state.chatMessages)
export const useAIMessages = () => useAppStore((state) => state.aiMessages)
export const useCanvasData = () => useAppStore((state) => state.canvasData)
export const useUI = () => useAppStore((state) => state.ui)
export const useMediaSettings = () => useAppStore((state) => state.mediaSettings)
export const usePeerConnections = () => useAppStore((state) => state.peerConnections)
export const useLocalStream = () => useAppStore((state) => state.localStream)
