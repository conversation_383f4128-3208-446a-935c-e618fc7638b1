const WebSocket = require('ws')
const http = require('http')
const url = require('url')

const PORT = process.env.WS_PORT || 3001

// Store rooms and their data
const rooms = new Map()

// Room structure: { users: Map, messages: [], canvasData: null }
function createRoom(roomId) {
  if (!rooms.has(roomId)) {
    rooms.set(roomId, {
      users: new Map(),
      messages: [],
      canvasData: null,
    })
  }
  return rooms.get(roomId)
}

// Create HTTP server
const server = http.createServer()

// Create WebSocket server
const wss = new WebSocket.Server({ 
  server,
  verifyClient: (info) => {
    // Allow all connections for now
    return true
  }
})

wss.on('connection', (ws, request) => {
  const query = url.parse(request.url, true).query
  const roomId = query.room
  
  if (!roomId) {
    ws.close(1008, 'Room ID required')
    return
  }

  console.log(`New connection to room: ${roomId}`)
  
  const room = createRoom(roomId)
  let userId = null

  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString())
      
      switch (message.type) {
        case 'user-join':
          userId = message.user.id
          room.users.set(userId, { ...message.user, ws })
          
          // Send current room state to new user
          ws.send(JSON.stringify({
            type: 'room-state',
            users: Array.from(room.users.values()).map(u => ({ id: u.id, name: u.name, color: u.color })),
            messages: room.messages,
            canvasData: room.canvasData,
          }))
          
          // Notify others about new user
          broadcast(room, {
            type: 'user-join',
            user: { id: message.user.id, name: message.user.name, color: message.user.color },
          }, userId)
          
          console.log(`User ${message.user.name} joined room ${roomId}`)
          break

        case 'user-leave':
          if (userId) {
            room.users.delete(userId)
            broadcast(room, {
              type: 'user-leave',
              userId: userId,
            })
          }
          break

        case 'cursor-update':
          if (userId) {
            broadcast(room, {
              type: 'cursor-update',
              userId: userId,
              cursor: message.cursor,
            }, userId)
          }
          break

        case 'chat-message':
          room.messages.push(message.message)
          // Keep only last 100 messages
          if (room.messages.length > 100) {
            room.messages = room.messages.slice(-100)
          }
          
          broadcast(room, {
            type: 'chat-message',
            message: message.message,
          })
          break

        case 'canvas-update':
          room.canvasData = message.data
          broadcast(room, {
            type: 'canvas-update',
            data: message.data,
          }, userId)
          break

        default:
          console.warn('Unknown message type:', message.type)
      }
    } catch (error) {
      console.error('Error processing message:', error)
    }
  })

  ws.on('close', () => {
    if (userId && room.users.has(userId)) {
      room.users.delete(userId)
      broadcast(room, {
        type: 'user-leave',
        userId: userId,
      })
      console.log(`User ${userId} left room ${roomId}`)
    }
    
    // Clean up empty rooms
    if (room.users.size === 0) {
      rooms.delete(roomId)
      console.log(`Room ${roomId} cleaned up`)
    }
  })

  ws.on('error', (error) => {
    console.error('WebSocket error:', error)
  })
})

function broadcast(room, message, excludeUserId = null) {
  const messageStr = JSON.stringify(message)
  
  room.users.forEach((user, id) => {
    if (id !== excludeUserId && user.ws.readyState === WebSocket.OPEN) {
      try {
        user.ws.send(messageStr)
      } catch (error) {
        console.error('Error broadcasting to user:', id, error)
        // Remove dead connections
        room.users.delete(id)
      }
    }
  })
}

server.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down WebSocket server...')
  wss.close(() => {
    server.close(() => {
      process.exit(0)
    })
  })
})

process.on('SIGINT', () => {
  console.log('Shutting down WebSocket server...')
  wss.close(() => {
    server.close(() => {
      process.exit(0)
    })
  })
})
