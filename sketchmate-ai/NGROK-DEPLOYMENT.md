# 🌐 SketchMate-AI ngrok Deployment Guide

This guide helps you deploy SketchMate-AI using ngrok so others can access your collaborative whiteboard from anywhere in the world.

## 📋 Prerequisites

### For Mac/Linux:
- Node.js (v18 or higher)
- npm
- ngrok (install via: `brew install ngrok` or download from https://ngrok.com/download)

### For Windows:
- Node.js (v18 or higher)
- npm
- ngrok (download from https://ngrok.com/download or install via: `choco install ngrok`)

## 🚀 Quick Start

### Mac/Linux:
```bash
# Make the script executable (if not already)
chmod +x start-ngrok.sh

# Run the deployment script
./start-ngrok.sh
```

### Windows:
```cmd
# Double-click the start-ngrok.bat file
# OR run from command prompt:
start-ngrok.bat
```

## 📱 What the Script Does

1. **Validates Prerequisites**: Checks if Node.js, npm, and ngrok are installed
2. **Sets up ngrok**: Configures your ngrok authtoken automatically
3. **Cleans up**: Kills any existing processes on ports 3000 and 3001
4. **Installs Dependencies**: Runs `npm install` if needed
5. **Starts WebSocket Server**: Launches the real-time collaboration server on port 3001
6. **Starts Next.js App**: Launches the main application on port 3000
7. **Creates ngrok Tunnel**: Exposes your local app to the internet
8. **Provides URLs**: Shows you the public URL to share with others

## 🌍 Accessing Your Application

After running the script, you'll see output like:

```
🎉 SketchMate-AI is now running!
=========================================
📱 Local URLs:
   • Next.js App: http://localhost:3000
   • WebSocket:   http://localhost:3001

🌍 Public URL:
   • Check ngrok dashboard: http://localhost:4040
   • Or look for the public URL in the ngrok output above
```

### Finding Your Public URL:

1. **From ngrok output**: Look for a line like `https://abc123.ngrok.io`
2. **From ngrok dashboard**: Visit http://localhost:4040 in your browser
3. **From ngrok web interface**: The dashboard shows all active tunnels

## 📋 Sharing with Others

1. Copy the ngrok public URL (e.g., `https://abc123.ngrok.io`)
2. Share this URL with collaborators
3. They can access your SketchMate-AI application from anywhere
4. Real-time collaboration will work seamlessly

## 🛑 Stopping the Application

### Mac/Linux:
- Press `Ctrl+C` in the terminal where the script is running
- The script will automatically clean up all processes

### Windows:
- Close the command prompt window
- Or press `Ctrl+C` in the command prompt

## 🔧 Troubleshooting

### Port Already in Use:
The script automatically kills existing processes on ports 3000 and 3001.

### ngrok Not Found:
Install ngrok:
- **Mac**: `brew install ngrok`
- **Windows**: Download from https://ngrok.com/download
- **Linux**: Download from https://ngrok.com/download

### Node.js Not Found:
Install Node.js from https://nodejs.org/

### WebSocket Connection Issues:
- Ensure both ports 3000 and 3001 are accessible
- Check your firewall settings
- Verify ngrok tunnel is active

## 🔐 Security Notes

- The ngrok authtoken is embedded in the script for convenience
- ngrok free tier has limitations (connections, bandwidth)
- For production use, consider ngrok paid plans or other deployment options

## 📞 Support

If you encounter issues:
1. Check the console output for error messages
2. Verify all prerequisites are installed
3. Try restarting the script
4. Check ngrok dashboard at http://localhost:4040

## 🎨 Features Available via ngrok

When others access your ngrok URL, they can:
- ✅ Draw on the collaborative whiteboard
- ✅ See real-time cursor movements
- ✅ Chat with other users
- ✅ Use AI assistant features
- ✅ Export and share drawings
- ✅ Join/create rooms

Happy collaborating! 🎉
