{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next'\nimport { Inter } from 'next/font/google'\nimport './globals.css'\n\nconst inter = Inter({ subsets: ['latin'] })\n\nexport const metadata: Metadata = {\n  title: 'SketchMate AI - Collaborative Whiteboard',\n  description: 'AI-powered collaborative whiteboard with real-time drawing, chat, and seamless sharing',\n  keywords: ['whiteboard', 'collaboration', 'AI', 'drawing', 'real-time'],\n  authors: [{ name: 'SketchMate Team' }],\n  viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',\n}\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <head>\n        <meta name=\"theme-color\" content=\"#ffffff\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n      </head>\n      <body className={`${inter.className} antialiased`}>\n        <div className=\"h-screen w-screen overflow-hidden bg-background\">\n          {children}\n        </div>\n      </body>\n    </html>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;AAMO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAc;QAAiB;QAAM;QAAW;KAAY;IACvE,SAAS;QAAC;YAAE,MAAM;QAAkB;KAAE;IACtC,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;;;;;;;0BAExB,8OAAC;gBAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,YAAY,CAAC;0BAC/C,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}