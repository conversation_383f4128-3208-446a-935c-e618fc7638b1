{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/stores/useAppStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport type { AppState, User, ChatMessage, AIMessage, UIState, MediaSettings, PeerConnection } from '@/types'\n\nconst initialUIState: UIState = {\n  isChatOpen: false,\n  isAIOpen: false,\n  isToolbarCollapsed: false,\n  isMenuOpen: false,\n  theme: 'light',\n}\n\nconst initialMediaSettings: MediaSettings = {\n  audio: false,\n  video: false,\n  screen: false,\n}\n\nexport const useAppStore = create<AppState>()(\n  devtools(\n    (set, get) => ({\n      // Initial State\n      currentUser: null,\n      users: new Map(),\n      roomId: null,\n      isConnected: false,\n      chatMessages: [],\n      aiMessages: [],\n      canvasData: null,\n      ui: initialUIState,\n      mediaSettings: initialMediaSettings,\n      peerConnections: new Map(),\n      localStream: null,\n\n      // User Actions\n      setCurrentUser: (user: User) => {\n        set({ currentUser: user }, false, 'setCurrentUser')\n      },\n\n      addUser: (user: User) => {\n        set((state) => {\n          const newUsers = new Map(state.users)\n          newUsers.set(user.id, user)\n          return { users: newUsers }\n        }, false, 'addUser')\n      },\n\n      removeUser: (userId: string) => {\n        set((state) => {\n          const newUsers = new Map(state.users)\n          newUsers.delete(userId)\n          return { users: newUsers }\n        }, false, 'removeUser')\n      },\n\n      updateUserCursor: (userId: string, cursor: { x: number; y: number }) => {\n        set((state) => {\n          const newUsers = new Map(state.users)\n          const user = newUsers.get(userId)\n          if (user) {\n            newUsers.set(userId, { ...user, cursor })\n          }\n          return { users: newUsers }\n        }, false, 'updateUserCursor')\n      },\n\n      // Room Actions\n      setRoomId: (roomId: string) => {\n        set({ roomId }, false, 'setRoomId')\n      },\n\n      setConnected: (connected: boolean) => {\n        set({ isConnected: connected }, false, 'setConnected')\n      },\n\n      // Chat Actions\n      addChatMessage: (message: ChatMessage) => {\n        set((state) => ({\n          chatMessages: [...state.chatMessages, message]\n        }), false, 'addChatMessage')\n      },\n\n      addAIMessage: (message: AIMessage) => {\n        set((state) => ({\n          aiMessages: [...state.aiMessages, message]\n        }), false, 'addAIMessage')\n      },\n\n      updateAIMessage: (id: string, content: string, isStreaming = false) => {\n        set((state) => ({\n          aiMessages: state.aiMessages.map(msg =>\n            msg.id === id ? { ...msg, content, isStreaming } : msg\n          )\n        }), false, 'updateAIMessage')\n      },\n\n      // Canvas Actions\n      updateCanvasData: (data: any) => {\n        set({ canvasData: data }, false, 'updateCanvasData')\n      },\n\n      // UI Actions\n      updateUI: (updates: Partial<UIState>) => {\n        set((state) => ({\n          ui: { ...state.ui, ...updates }\n        }), false, 'updateUI')\n      },\n\n      // Media Actions\n      updateMediaSettings: (settings: Partial<MediaSettings>) => {\n        set((state) => ({\n          mediaSettings: { ...state.mediaSettings, ...settings }\n        }), false, 'updateMediaSettings')\n      },\n\n      addPeerConnection: (userId: string, connection: PeerConnection) => {\n        set((state) => {\n          const newConnections = new Map(state.peerConnections)\n          newConnections.set(userId, connection)\n          return { peerConnections: newConnections }\n        }, false, 'addPeerConnection')\n      },\n\n      removePeerConnection: (userId: string) => {\n        set((state) => {\n          const newConnections = new Map(state.peerConnections)\n          newConnections.delete(userId)\n          return { peerConnections: newConnections }\n        }, false, 'removePeerConnection')\n      },\n\n      setLocalStream: (stream: MediaStream | null) => {\n        set({ localStream: stream }, false, 'setLocalStream')\n      },\n    }),\n    {\n      name: 'sketchmate-store',\n    }\n  )\n)\n\n// Selectors for optimized re-renders\nexport const useCurrentUser = () => useAppStore((state) => state.currentUser)\nexport const useUsers = () => useAppStore((state) => state.users)\nexport const useRoomId = () => useAppStore((state) => state.roomId)\nexport const useIsConnected = () => useAppStore((state) => state.isConnected)\nexport const useChatMessages = () => useAppStore((state) => state.chatMessages)\nexport const useAIMessages = () => useAppStore((state) => state.aiMessages)\nexport const useCanvasData = () => useAppStore((state) => state.canvasData)\nexport const useUI = () => useAppStore((state) => state.ui)\nexport const useMediaSettings = () => useAppStore((state) => state.mediaSettings)\nexport const usePeerConnections = () => useAppStore((state) => state.peerConnections)\nexport const useLocalStream = () => useAppStore((state) => state.localStream)\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAGA,MAAM,iBAA0B;IAC9B,YAAY;IACZ,UAAU;IACV,oBAAoB;IACpB,YAAY;IACZ,OAAO;AACT;AAEA,MAAM,uBAAsC;IAC1C,OAAO;IACP,OAAO;IACP,QAAQ;AACV;AAEO,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,aAAa;QACb,OAAO,IAAI;QACX,QAAQ;QACR,aAAa;QACb,cAAc,EAAE;QAChB,YAAY,EAAE;QACd,YAAY;QACZ,IAAI;QACJ,eAAe;QACf,iBAAiB,IAAI;QACrB,aAAa;QAEb,eAAe;QACf,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAK,GAAG,OAAO;QACpC;QAEA,SAAS,CAAC;YACR,IAAI,CAAC;gBACH,MAAM,WAAW,IAAI,IAAI,MAAM,KAAK;gBACpC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;gBACtB,OAAO;oBAAE,OAAO;gBAAS;YAC3B,GAAG,OAAO;QACZ;QAEA,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,WAAW,IAAI,IAAI,MAAM,KAAK;gBACpC,SAAS,MAAM,CAAC;gBAChB,OAAO;oBAAE,OAAO;gBAAS;YAC3B,GAAG,OAAO;QACZ;QAEA,kBAAkB,CAAC,QAAgB;YACjC,IAAI,CAAC;gBACH,MAAM,WAAW,IAAI,IAAI,MAAM,KAAK;gBACpC,MAAM,OAAO,SAAS,GAAG,CAAC;gBAC1B,IAAI,MAAM;oBACR,SAAS,GAAG,CAAC,QAAQ;wBAAE,GAAG,IAAI;wBAAE;oBAAO;gBACzC;gBACA,OAAO;oBAAE,OAAO;gBAAS;YAC3B,GAAG,OAAO;QACZ;QAEA,eAAe;QACf,WAAW,CAAC;YACV,IAAI;gBAAE;YAAO,GAAG,OAAO;QACzB;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE,aAAa;YAAU,GAAG,OAAO;QACzC;QAEA,eAAe;QACf,gBAAgB,CAAC;YACf,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;2BAAI,MAAM,YAAY;wBAAE;qBAAQ;gBAChD,CAAC,GAAG,OAAO;QACb;QAEA,cAAc,CAAC;YACb,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY;2BAAI,MAAM,UAAU;wBAAE;qBAAQ;gBAC5C,CAAC,GAAG,OAAO;QACb;QAEA,iBAAiB,CAAC,IAAY,SAAiB,cAAc,KAAK;YAChE,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC,CAAA,MAC/B,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE;4BAAS;wBAAY,IAAI;gBAEvD,CAAC,GAAG,OAAO;QACb;QAEA,iBAAiB;QACjB,kBAAkB,CAAC;YACjB,IAAI;gBAAE,YAAY;YAAK,GAAG,OAAO;QACnC;QAEA,aAAa;QACb,UAAU,CAAC;YACT,IAAI,CAAC,QAAU,CAAC;oBACd,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,GAAG,OAAO;oBAAC;gBAChC,CAAC,GAAG,OAAO;QACb;QAEA,gBAAgB;QAChB,qBAAqB,CAAC;YACpB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAE,GAAG,MAAM,aAAa;wBAAE,GAAG,QAAQ;oBAAC;gBACvD,CAAC,GAAG,OAAO;QACb;QAEA,mBAAmB,CAAC,QAAgB;YAClC,IAAI,CAAC;gBACH,MAAM,iBAAiB,IAAI,IAAI,MAAM,eAAe;gBACpD,eAAe,GAAG,CAAC,QAAQ;gBAC3B,OAAO;oBAAE,iBAAiB;gBAAe;YAC3C,GAAG,OAAO;QACZ;QAEA,sBAAsB,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,iBAAiB,IAAI,IAAI,MAAM,eAAe;gBACpD,eAAe,MAAM,CAAC;gBACtB,OAAO;oBAAE,iBAAiB;gBAAe;YAC3C,GAAG,OAAO;QACZ;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAO,GAAG,OAAO;QACtC;IACF,CAAC,GACD;IACE,MAAM;AACR;AAKG,MAAM,iBAAiB,IAAM,YAAY,CAAC,QAAU,MAAM,WAAW;AACrE,MAAM,WAAW,IAAM,YAAY,CAAC,QAAU,MAAM,KAAK;AACzD,MAAM,YAAY,IAAM,YAAY,CAAC,QAAU,MAAM,MAAM;AAC3D,MAAM,iBAAiB,IAAM,YAAY,CAAC,QAAU,MAAM,WAAW;AACrE,MAAM,kBAAkB,IAAM,YAAY,CAAC,QAAU,MAAM,YAAY;AACvE,MAAM,gBAAgB,IAAM,YAAY,CAAC,QAAU,MAAM,UAAU;AACnE,MAAM,gBAAgB,IAAM,YAAY,CAAC,QAAU,MAAM,UAAU;AACnE,MAAM,QAAQ,IAAM,YAAY,CAAC,QAAU,MAAM,EAAE;AACnD,MAAM,mBAAmB,IAAM,YAAY,CAAC,QAAU,MAAM,aAAa;AACzE,MAAM,qBAAqB,IAAM,YAAY,CAAC,QAAU,MAAM,eAAe;AAC7E,MAAM,iBAAiB,IAAM,YAAY,CAAC,QAAU,MAAM,WAAW", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/types/index.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// User Schema\nexport const UserSchema = z.object({\n  id: z.string(),\n  name: z.string(),\n  avatar: z.string().optional(),\n  color: z.string(),\n  cursor: z.object({\n    x: z.number(),\n    y: z.number(),\n  }).optional(),\n})\n\nexport type User = z.infer<typeof UserSchema>\n\n// Chat Message Schema\nexport const ChatMessageSchema = z.object({\n  id: z.string(),\n  userId: z.string(),\n  userName: z.string(),\n  content: z.string(),\n  timestamp: z.number(),\n  type: z.enum(['text', 'system']).default('text'),\n})\n\nexport type ChatMessage = z.infer<typeof ChatMessageSchema>\n\n// AI Message Schema\nexport const AIMessageSchema = z.object({\n  id: z.string(),\n  content: z.string(),\n  timestamp: z.number(),\n  isStreaming: z.boolean().default(false),\n  canvasContext: z.string().optional(),\n})\n\nexport type AIMessage = z.infer<typeof AIMessageSchema>\n\n// WebSocket Message Schema\nexport const WSMessageSchema = z.discriminatedUnion('type', [\n  z.object({\n    type: z.literal('user-join'),\n    user: UserSchema,\n  }),\n  z.object({\n    type: z.literal('user-leave'),\n    userId: z.string(),\n  }),\n  z.object({\n    type: z.literal('cursor-update'),\n    userId: z.string(),\n    cursor: z.object({\n      x: z.number(),\n      y: z.number(),\n    }),\n  }),\n  z.object({\n    type: z.literal('chat-message'),\n    message: ChatMessageSchema,\n  }),\n  z.object({\n    type: z.literal('canvas-update'),\n    data: z.any(), // Excalidraw data\n  }),\n  z.object({\n    type: z.literal('room-state'),\n    users: z.array(UserSchema),\n    messages: z.array(ChatMessageSchema),\n    canvasData: z.any().nullable(),\n  }),\n])\n\nexport type WSMessage = z.infer<typeof WSMessageSchema>\n\n// Room Schema\nexport const RoomSchema = z.object({\n  id: z.string(),\n  users: z.map(z.string(), UserSchema),\n  messages: z.array(ChatMessageSchema),\n  canvasData: z.any().nullable(),\n})\n\nexport type Room = z.infer<typeof RoomSchema>\n\n// UI State Types\nexport interface UIState {\n  isChatOpen: boolean\n  isAIOpen: boolean\n  isToolbarCollapsed: boolean\n  isMenuOpen: boolean\n  theme: 'light' | 'dark'\n}\n\n// Canvas Types\nexport interface CanvasState {\n  elements: any[]\n  appState: any\n  files: any\n}\n\n// Media Types\nexport interface MediaSettings {\n  audio: boolean\n  video: boolean\n  screen: boolean\n}\n\nexport interface PeerConnection {\n  userId: string\n  connection: RTCPeerConnection\n  stream?: MediaStream\n}\n\n// App Store State\nexport interface AppState {\n  // User Management\n  currentUser: User | null\n  users: Map<string, User>\n  \n  // Room Management\n  roomId: string | null\n  isConnected: boolean\n  \n  // Chat\n  chatMessages: ChatMessage[]\n  aiMessages: AIMessage[]\n  \n  // Canvas\n  canvasData: any\n  \n  // UI\n  ui: UIState\n  \n  // Media\n  mediaSettings: MediaSettings\n  peerConnections: Map<string, PeerConnection>\n  localStream: MediaStream | null\n  \n  // Actions\n  setCurrentUser: (user: User) => void\n  addUser: (user: User) => void\n  removeUser: (userId: string) => void\n  updateUserCursor: (userId: string, cursor: { x: number; y: number }) => void\n  \n  setRoomId: (roomId: string) => void\n  setConnected: (connected: boolean) => void\n  \n  addChatMessage: (message: ChatMessage) => void\n  addAIMessage: (message: AIMessage) => void\n  updateAIMessage: (id: string, content: string, isStreaming?: boolean) => void\n  \n  updateCanvasData: (data: any) => void\n  \n  updateUI: (updates: Partial<UIState>) => void\n  \n  updateMediaSettings: (settings: Partial<MediaSettings>) => void\n  addPeerConnection: (userId: string, connection: PeerConnection) => void\n  removePeerConnection: (userId: string) => void\n  setLocalStream: (stream: MediaStream | null) => void\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGO,MAAM,aAAa,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,IAAI,+JAAA,CAAA,IAAC,CAAC,MAAM;IACZ,MAAM,+JAAA,CAAA,IAAC,CAAC,MAAM;IACd,QAAQ,+JAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,OAAO,+JAAA,CAAA,IAAC,CAAC,MAAM;IACf,QAAQ,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACf,GAAG,+JAAA,CAAA,IAAC,CAAC,MAAM;QACX,GAAG,+JAAA,CAAA,IAAC,CAAC,MAAM;IACb,GAAG,QAAQ;AACb;AAKO,MAAM,oBAAoB,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,+JAAA,CAAA,IAAC,CAAC,MAAM;IACZ,QAAQ,+JAAA,CAAA,IAAC,CAAC,MAAM;IAChB,UAAU,+JAAA,CAAA,IAAC,CAAC,MAAM;IAClB,SAAS,+JAAA,CAAA,IAAC,CAAC,MAAM;IACjB,WAAW,+JAAA,CAAA,IAAC,CAAC,MAAM;IACnB,MAAM,+JAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;KAAS,EAAE,OAAO,CAAC;AAC3C;AAKO,MAAM,kBAAkB,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,IAAI,+JAAA,CAAA,IAAC,CAAC,MAAM;IACZ,SAAS,+JAAA,CAAA,IAAC,CAAC,MAAM;IACjB,WAAW,+JAAA,CAAA,IAAC,CAAC,MAAM;IACnB,aAAa,+JAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACjC,eAAe,+JAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACpC;AAKO,MAAM,kBAAkB,+JAAA,CAAA,IAAC,CAAC,kBAAkB,CAAC,QAAQ;IAC1D,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,+JAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QAChB,MAAM;IACR;IACA,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,+JAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,+JAAA,CAAA,IAAC,CAAC,MAAM;IAClB;IACA,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,+JAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,+JAAA,CAAA,IAAC,CAAC,MAAM;QAChB,QAAQ,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACf,GAAG,+JAAA,CAAA,IAAC,CAAC,MAAM;YACX,GAAG,+JAAA,CAAA,IAAC,CAAC,MAAM;QACb;IACF;IACA,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,+JAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QAChB,SAAS;IACX;IACA,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,+JAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QAChB,MAAM,+JAAA,CAAA,IAAC,CAAC,GAAG;IACb;IACA,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,MAAM,+JAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QAChB,OAAO,+JAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QACf,UAAU,+JAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAClB,YAAY,+JAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;IAC9B;CACD;AAKM,MAAM,aAAa,+JAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,IAAI,+JAAA,CAAA,IAAC,CAAC,MAAM;IACZ,OAAO,+JAAA,CAAA,IAAC,CAAC,GAAG,CAAC,+JAAA,CAAA,IAAC,CAAC,MAAM,IAAI;IACzB,UAAU,+JAAA,CAAA,IAAC,CAAC,KAAK,CAAC;IAClB,YAAY,+JAAA,CAAA,IAAC,CAAC,GAAG,GAAG,QAAQ;AAC9B", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/hooks/useWebSocket.ts"], "sourcesContent": ["import { useEffect, useRef, useCallback } from 'react'\nimport { useAppStore } from '@/stores/useAppStore'\nimport { WSMessage, WSMessageSchema } from '@/types'\n\nexport function useWebSocket(roomId: string | null) {\n  const wsRef = useRef<WebSocket | null>(null)\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)\n  const reconnectAttempts = useRef(0)\n  const maxReconnectAttempts = 5\n\n  const {\n    setConnected,\n    addUser,\n    removeUser,\n    updateUserCursor,\n    addChatMessage,\n    updateCanvasData,\n    currentUser,\n  } = useAppStore()\n\n  const connect = useCallback(() => {\n    if (!roomId || wsRef.current?.readyState === WebSocket.OPEN) return\n\n    try {\n      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'\n      const ws = new WebSocket(`${wsUrl}?room=${roomId}`)\n      wsRef.current = ws\n\n      ws.onopen = () => {\n        console.log('WebSocket connected')\n        setConnected(true)\n        reconnectAttempts.current = 0\n\n        // Join room with current user\n        if (currentUser) {\n          sendMessage({\n            type: 'user-join',\n            user: currentUser,\n          })\n        }\n      }\n\n      ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data)\n          const message = WSMessageSchema.parse(data)\n          handleMessage(message)\n        } catch (error) {\n          console.error('Failed to parse WebSocket message:', error)\n        }\n      }\n\n      ws.onclose = (event) => {\n        console.log('WebSocket disconnected:', event.code, event.reason)\n        setConnected(false)\n        wsRef.current = null\n\n        // Attempt to reconnect if not a manual close\n        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {\n          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000)\n          reconnectTimeoutRef.current = setTimeout(() => {\n            reconnectAttempts.current++\n            connect()\n          }, delay)\n        }\n      }\n\n      ws.onerror = (error) => {\n        console.error('WebSocket error:', error)\n      }\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error)\n    }\n  }, [roomId, currentUser, setConnected])\n\n  const disconnect = useCallback(() => {\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current)\n      reconnectTimeoutRef.current = null\n    }\n\n    if (wsRef.current) {\n      wsRef.current.close(1000, 'Manual disconnect')\n      wsRef.current = null\n    }\n    setConnected(false)\n  }, [setConnected])\n\n  const sendMessage = useCallback((message: WSMessage) => {\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\n      wsRef.current.send(JSON.stringify(message))\n    } else {\n      console.warn('WebSocket not connected, message not sent:', message)\n    }\n  }, [])\n\n  const handleMessage = useCallback((message: WSMessage) => {\n    switch (message.type) {\n      case 'user-join':\n        addUser(message.user)\n        break\n\n      case 'user-leave':\n        removeUser(message.userId)\n        break\n\n      case 'cursor-update':\n        updateUserCursor(message.userId, message.cursor)\n        break\n\n      case 'chat-message':\n        addChatMessage(message.message)\n        break\n\n      case 'canvas-update':\n        updateCanvasData(message.data)\n        break\n\n      case 'room-state':\n        // Handle initial room state\n        message.users.forEach(user => addUser(user))\n        message.messages.forEach(msg => addChatMessage(msg))\n        if (message.canvasData) {\n          updateCanvasData(message.canvasData)\n        }\n        break\n\n      default:\n        console.warn('Unknown message type:', message)\n    }\n  }, [addUser, removeUser, updateUserCursor, addChatMessage, updateCanvasData])\n\n  // Connect when roomId changes\n  useEffect(() => {\n    if (roomId) {\n      connect()\n    } else {\n      disconnect()\n    }\n\n    return () => {\n      disconnect()\n    }\n  }, [roomId, connect, disconnect])\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      disconnect()\n    }\n  }, [disconnect])\n\n  return {\n    sendMessage,\n    isConnected: wsRef.current?.readyState === WebSocket.OPEN,\n    connect,\n    disconnect,\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,SAAS,aAAa,MAAqB;IAChD,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACvC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAC1D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,uBAAuB;IAE7B,MAAM,EACJ,YAAY,EACZ,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,WAAW,EACZ,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,CAAC,UAAU,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;QAE7D,IAAI;YACF,MAAM,QAAQ,2DAAkC;YAChD,MAAM,KAAK,IAAI,UAAU,GAAG,MAAM,MAAM,EAAE,QAAQ;YAClD,MAAM,OAAO,GAAG;YAEhB,GAAG,MAAM,GAAG;gBACV,QAAQ,GAAG,CAAC;gBACZ,aAAa;gBACb,kBAAkB,OAAO,GAAG;gBAE5B,8BAA8B;gBAC9B,IAAI,aAAa;oBACf,YAAY;wBACV,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;YAEA,GAAG,SAAS,GAAG,CAAC;gBACd,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;oBAClC,MAAM,UAAU,qHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC;oBACtC,cAAc;gBAChB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF;YAEA,GAAG,OAAO,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,2BAA2B,MAAM,IAAI,EAAE,MAAM,MAAM;gBAC/D,aAAa;gBACb,MAAM,OAAO,GAAG;gBAEhB,6CAA6C;gBAC7C,IAAI,MAAM,IAAI,KAAK,QAAQ,kBAAkB,OAAO,GAAG,sBAAsB;oBAC3E,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,kBAAkB,OAAO,GAAG;oBACtE,oBAAoB,OAAO,GAAG,WAAW;wBACvC,kBAAkB,OAAO;wBACzB;oBACF,GAAG;gBACL;YACF;YAEA,GAAG,OAAO,GAAG,CAAC;gBACZ,QAAQ,KAAK,CAAC,oBAAoB;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF,GAAG;QAAC;QAAQ;QAAa;KAAa;IAEtC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,oBAAoB,OAAO,EAAE;YAC/B,aAAa,oBAAoB,OAAO;YACxC,oBAAoB,OAAO,GAAG;QAChC;QAEA,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM;YAC1B,MAAM,OAAO,GAAG;QAClB;QACA,aAAa;IACf,GAAG;QAAC;KAAa;IAEjB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;YAChD,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QACpC,OAAO;YACL,QAAQ,IAAI,CAAC,8CAA8C;QAC7D;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,QAAQ,QAAQ,IAAI;gBACpB;YAEF,KAAK;gBACH,WAAW,QAAQ,MAAM;gBACzB;YAEF,KAAK;gBACH,iBAAiB,QAAQ,MAAM,EAAE,QAAQ,MAAM;gBAC/C;YAEF,KAAK;gBACH,eAAe,QAAQ,OAAO;gBAC9B;YAEF,KAAK;gBACH,iBAAiB,QAAQ,IAAI;gBAC7B;YAEF,KAAK;gBACH,4BAA4B;gBAC5B,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAA,OAAQ,QAAQ;gBACtC,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,MAAO,eAAe;gBAC/C,IAAI,QAAQ,UAAU,EAAE;oBACtB,iBAAiB,QAAQ,UAAU;gBACrC;gBACA;YAEF;gBACE,QAAQ,IAAI,CAAC,yBAAyB;QAC1C;IACF,GAAG;QAAC;QAAS;QAAY;QAAkB;QAAgB;KAAiB;IAE5E,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF,OAAO;YACL;QACF;QAEA,OAAO;YACL;QACF;IACF,GAAG;QAAC;QAAQ;QAAS;KAAW;IAEhC,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA,aAAa,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI;QACzD;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\n// import { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\nexport function generateUserId(): string {\n  return `user_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`\n}\n\nexport function generateUserColor(): string {\n  const colors = [\n    '#FF6B6B', // Red\n    '#4ECDC4', // Teal\n    '#45B7D1', // Blue\n    '#96CEB4', // Green\n    '#FFEAA7', // Yellow\n    '#DDA0DD', // Plum\n    '#98D8C8', // Mint\n    '#F7DC6F', // Light Yellow\n    '#BB8FCE', // Light Purple\n    '#85C1E9', // Light Blue\n    '#F8C471', // Orange\n    '#82E0AA', // Light Green\n  ]\n  return colors[Math.floor(Math.random() * colors.length)]\n}\n\nexport function generateRoomId(): string {\n  return Math.random().toString(36).substring(2, 8).toUpperCase()\n}\n\nexport function formatTimestamp(timestamp: number): string {\n  const date = new Date(timestamp)\n  const now = new Date()\n  const diff = now.getTime() - date.getTime()\n  \n  // Less than 1 minute\n  if (diff < 60000) {\n    return 'just now'\n  }\n  \n  // Less than 1 hour\n  if (diff < 3600000) {\n    const minutes = Math.floor(diff / 60000)\n    return `${minutes}m ago`\n  }\n  \n  // Less than 24 hours\n  if (diff < 86400000) {\n    const hours = Math.floor(diff / 3600000)\n    return `${hours}h ago`\n  }\n  \n  // More than 24 hours\n  return date.toLocaleDateString()\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) {\n      clearTimeout(timeout)\n    }\n    \n    timeout = setTimeout(() => {\n      func(...args)\n    }, wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean = false\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => {\n        inThrottle = false\n      }, limit)\n    }\n  }\n}\n\nexport function copyToClipboard(text: string): Promise<boolean> {\n  // Check if we're in a browser environment\n  if (typeof window === 'undefined' || typeof navigator === 'undefined') {\n    return Promise.resolve(false)\n  }\n\n  if (navigator.clipboard && window.isSecureContext) {\n    return navigator.clipboard.writeText(text).then(() => true).catch(() => false)\n  } else {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea')\n    textArea.value = text\n    textArea.style.position = 'absolute'\n    textArea.style.left = '-999999px'\n    document.body.prepend(textArea)\n    textArea.select()\n\n    try {\n      document.execCommand('copy')\n      return Promise.resolve(true)\n    } catch (error) {\n      return Promise.resolve(false)\n    } finally {\n      textArea.remove()\n    }\n  }\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .substring(0, 2)\n}\n\nexport function isValidRoomId(roomId: string): boolean {\n  return /^[a-zA-Z0-9]{6}$/.test(roomId)\n}\n\nexport function sanitizeFileName(fileName: string): string {\n  return fileName.replace(/[^a-z0-9]/gi, '_').toLowerCase()\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function getRandomElement<T>(array: T[]): T {\n  return array[Math.floor(Math.random() * array.length)]\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS;IACd,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI;AAC3E;AAEO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;AAC/D;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;IAEzC,qBAAqB;IACrB,IAAI,OAAO,OAAO;QAChB,OAAO;IACT;IAEA,mBAAmB;IACnB,IAAI,OAAO,SAAS;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,OAAO,GAAG,QAAQ,KAAK,CAAC;IAC1B;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU;QACnB,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO;QAChC,OAAO,GAAG,MAAM,KAAK,CAAC;IACxB;IAEA,qBAAqB;IACrB,OAAO,KAAK,kBAAkB;AAChC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS;YACX,aAAa;QACf;QAEA,UAAU,WAAW;YACnB,QAAQ;QACV,GAAG;IACL;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI,aAAsB;IAE1B,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW;gBACT,aAAa;YACf,GAAG;QACL;IACF;AACF;AAEO,SAAS,gBAAgB,IAAY;IAC1C,0CAA0C;IAC1C,wCAAuE;QACrE,OAAO,QAAQ,OAAO,CAAC;IACzB;;AAsBF;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,SAAS,CAAC,GAAG;AAClB;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,mBAAmB,IAAI,CAAC;AACjC;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,OAAO,CAAC,eAAe,KAAK,WAAW;AACzD;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,iBAAoB,KAAU;IAC5C,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/ui/FloatingToolbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAppStore } from '@/stores/useAppStore'\nimport { Button } from './Button'\nimport { \n  Menu, \n  Users, \n  Settings, \n  Download, \n  Upload,\n  Maximize2,\n  Minimize2,\n  MoreHorizontal\n} from 'lucide-react'\n\nexport function FloatingToolbar() {\n  const { ui, updateUI, users } = useAppStore()\n  const [isExpanded, setIsExpanded] = useState(false)\n\n  const toggleExpanded = () => setIsExpanded(!isExpanded)\n\n  return (\n    <div className=\"floating-panel p-2\">\n      <div className=\"flex items-center gap-2\">\n        {/* Always visible: Menu toggle */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={() => updateUI({ isMenuOpen: !ui.isMenuOpen })}\n          className=\"h-8 w-8\"\n        >\n          <Menu className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Expandable section */}\n        {isExpanded && (\n          <div className=\"flex items-center gap-1 animate-fade-in\">\n            {/* Users indicator */}\n            <div className=\"flex items-center gap-1 px-2 py-1 rounded bg-secondary text-secondary-foreground\">\n              <Users className=\"h-3 w-3\" />\n              <span className=\"text-xs font-medium\">{users.size}</span>\n            </div>\n\n            {/* Toolbar actions */}\n            <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n              <Download className=\"h-4 w-4\" />\n            </Button>\n            \n            <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n              <Upload className=\"h-4 w-4\" />\n            </Button>\n            \n            <Button \n              variant=\"ghost\" \n              size=\"icon\" \n              className=\"h-8 w-8\"\n              onClick={() => updateUI({ isToolbarCollapsed: !ui.isToolbarCollapsed })}\n            >\n              {ui.isToolbarCollapsed ? (\n                <Maximize2 className=\"h-4 w-4\" />\n              ) : (\n                <Minimize2 className=\"h-4 w-4\" />\n              )}\n            </Button>\n            \n            <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        )}\n\n        {/* Expand/Collapse toggle */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={toggleExpanded}\n          className=\"h-8 w-8\"\n        >\n          <MoreHorizontal className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,IAAM,cAAc,CAAC;IAE5C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,SAAS;4BAAE,YAAY,CAAC,GAAG,UAAU;wBAAC;oBACrD,WAAU;8BAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;gBAIjB,4BACC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAuB,MAAM,IAAI;;;;;;;;;;;;sCAInD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;sCAC5C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAGtB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;sCAC5C,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAGpB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,SAAS;oCAAE,oBAAoB,CAAC,GAAG,kBAAkB;gCAAC;sCAEpE,GAAG,kBAAkB,iBACpB,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;qDAErB,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAIzB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;sCAC5C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAM1B,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKpC", "debugId": null}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/ui/Input.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/chat/ChatPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAppStore } from '@/stores/useAppStore'\nimport { useWebSocket } from '@/hooks/useWebSocket'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { ChatMessage } from '@/types'\nimport { formatTimestamp, generateUserId } from '@/lib/utils'\nimport { Send, X, MessageCircle } from 'lucide-react'\n\nexport function ChatPanel() {\n  const [message, setMessage] = useState('')\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  const {\n    chatMessages,\n    addChatMessage,\n    currentUser,\n    roomId,\n    updateUI,\n  } = useAppStore()\n\n  const { sendMessage } = useWebSocket(roomId)\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [chatMessages])\n\n  useEffect(() => {\n    // Focus input when panel opens\n    inputRef.current?.focus()\n  }, [])\n\n  const handleSendMessage = () => {\n    if (!message.trim() || !currentUser) return\n\n    const chatMessage: ChatMessage = {\n      id: generateUserId(),\n      userId: currentUser.id,\n      userName: currentUser.name,\n      content: message.trim(),\n      timestamp: Date.now(),\n      type: 'text',\n    }\n\n    addChatMessage(chatMessage)\n    \n    if (sendMessage) {\n      sendMessage({\n        type: 'chat-message',\n        message: chatMessage,\n      })\n    }\n\n    setMessage('')\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      handleSendMessage()\n    }\n  }\n\n  return (\n    <div className=\"floating-panel h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-border\">\n        <div className=\"flex items-center gap-2\">\n          <MessageCircle className=\"h-5 w-5\" />\n          <h3 className=\"font-semibold\">Chat</h3>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={() => updateUI({ isChatOpen: false })}\n          className=\"h-8 w-8\"\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-3\">\n        {chatMessages.length === 0 ? (\n          <div className=\"text-center text-muted-foreground py-8\">\n            <MessageCircle className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n            <p className=\"text-sm\">No messages yet</p>\n            <p className=\"text-xs\">Start a conversation!</p>\n          </div>\n        ) : (\n          chatMessages.map((msg) => (\n            <div\n              key={msg.id}\n              className={`flex flex-col space-y-1 ${\n                msg.userId === currentUser?.id ? 'items-end' : 'items-start'\n              }`}\n            >\n              <div\n                className={`max-w-[80%] rounded-lg px-3 py-2 text-sm ${\n                  msg.userId === currentUser?.id\n                    ? 'bg-primary text-primary-foreground'\n                    : 'bg-secondary text-secondary-foreground'\n                }`}\n              >\n                {msg.userId !== currentUser?.id && (\n                  <div className=\"text-xs font-medium mb-1 opacity-70\">\n                    {msg.userName}\n                  </div>\n                )}\n                <div className=\"whitespace-pre-wrap break-words\">\n                  {msg.content}\n                </div>\n              </div>\n              <div className=\"text-xs text-muted-foreground\">\n                {formatTimestamp(msg.timestamp)}\n              </div>\n            </div>\n          ))\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"p-4 border-t border-border\">\n        <div className=\"flex gap-2\">\n          <Input\n            ref={inputRef}\n            placeholder=\"Type a message...\"\n            value={message}\n            onChange={(e) => setMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            className=\"flex-1\"\n          />\n          <Button\n            onClick={handleSendMessage}\n            disabled={!message.trim()}\n            size=\"icon\"\n            className=\"shrink-0\"\n          >\n            <Send className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AATA;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,EACJ,YAAY,EACZ,cAAc,EACd,WAAW,EACX,MAAM,EACN,QAAQ,EACT,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IAErC,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,SAAS,OAAO,EAAE;IACpB,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,aAAa;QAErC,MAAM,cAA2B;YAC/B,IAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD;YACjB,QAAQ,YAAY,EAAE;YACtB,UAAU,YAAY,IAAI;YAC1B,SAAS,QAAQ,IAAI;YACrB,WAAW,KAAK,GAAG;YACnB,MAAM;QACR;QAEA,eAAe;QAEf,IAAI,aAAa;YACf,YAAY;gBACV,MAAM;gBACN,SAAS;YACX;QACF;QAEA,WAAW;IACb;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;;;;;;;kCAEhC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,SAAS;gCAAE,YAAY;4BAAM;wBAC5C,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,MAAM,KAAK,kBACvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;+BAGzB,aAAa,GAAG,CAAC,CAAC,oBAChB,8OAAC;4BAEC,WAAW,CAAC,wBAAwB,EAClC,IAAI,MAAM,KAAK,aAAa,KAAK,cAAc,eAC/C;;8CAEF,8OAAC;oCACC,WAAW,CAAC,yCAAyC,EACnD,IAAI,MAAM,KAAK,aAAa,KACxB,uCACA,0CACJ;;wCAED,IAAI,MAAM,KAAK,aAAa,oBAC3B,8OAAC;4CAAI,WAAU;sDACZ,IAAI,QAAQ;;;;;;sDAGjB,8OAAC;4CAAI,WAAU;sDACZ,IAAI,OAAO;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,SAAS;;;;;;;2BAtB3B,IAAI,EAAE;;;;;kCA2BjB,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,KAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC1C,YAAY;4BACZ,WAAU;;;;;;sCAEZ,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,QAAQ,IAAI;4BACvB,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/ai/AIPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAppStore } from '@/stores/useAppStore'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { AIMessage } from '@/types'\nimport { formatTimestamp, generateUserId } from '@/lib/utils'\nimport { Send, X, Bot, Sparkles } from 'lucide-react'\nimport ReactMarkdown from 'react-markdown'\nimport 'katex/dist/katex.min.css'\n\nexport function AIPanel() {\n  const [prompt, setPrompt] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  const {\n    aiMessages,\n    addAIMessage,\n    updateAIMessage,\n    canvasData,\n    updateUI,\n  } = useAppStore()\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [aiMessages])\n\n  useEffect(() => {\n    // Focus input when panel opens\n    inputRef.current?.focus()\n  }, [])\n\n  const handleSendPrompt = async () => {\n    if (!prompt.trim() || isLoading) return\n\n    const userPrompt = prompt.trim()\n    setPrompt('')\n    setIsLoading(true)\n\n    // Add user message\n    const userMessage: AIMessage = {\n      id: generateUserId(),\n      content: `**You:** ${userPrompt}`,\n      timestamp: Date.now(),\n      isStreaming: false,\n    }\n    addAIMessage(userMessage)\n\n    // Add AI response placeholder\n    const aiMessageId = generateUserId()\n    const aiMessage: AIMessage = {\n      id: aiMessageId,\n      content: '',\n      timestamp: Date.now(),\n      isStreaming: true,\n      canvasContext: canvasData ? JSON.stringify(canvasData) : undefined,\n    }\n    addAIMessage(aiMessage)\n\n    try {\n      // Call AI API\n      const response = await fetch('/api/ai/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          prompt: userPrompt,\n          canvasContext: canvasData,\n        }),\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to get AI response')\n      }\n\n      const reader = response.body?.getReader()\n      if (!reader) {\n        throw new Error('No response stream')\n      }\n\n      let accumulatedContent = ''\n      \n      while (true) {\n        const { done, value } = await reader.read()\n        \n        if (done) break\n        \n        const chunk = new TextDecoder().decode(value)\n        const lines = chunk.split('\\n')\n        \n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6))\n              if (data.content) {\n                accumulatedContent += data.content\n                updateAIMessage(aiMessageId, accumulatedContent, true)\n              }\n            } catch (e) {\n              // Ignore parsing errors for incomplete chunks\n            }\n          }\n        }\n      }\n\n      // Mark as complete\n      updateAIMessage(aiMessageId, accumulatedContent, false)\n      \n    } catch (error) {\n      console.error('AI request failed:', error)\n      updateAIMessage(\n        aiMessageId,\n        '❌ Sorry, I encountered an error. Please try again.',\n        false\n      )\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      handleSendPrompt()\n    }\n  }\n\n  return (\n    <div className=\"floating-panel h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-border\">\n        <div className=\"flex items-center gap-2\">\n          <Bot className=\"h-5 w-5 text-blue-600\" />\n          <h3 className=\"font-semibold\">AI Assistant</h3>\n          <Sparkles className=\"h-4 w-4 text-yellow-500\" />\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={() => updateUI({ isAIOpen: false })}\n          className=\"h-8 w-8\"\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {aiMessages.length === 0 ? (\n          <div className=\"text-center text-muted-foreground py-8\">\n            <Bot className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n            <p className=\"text-sm\">AI Assistant Ready</p>\n            <p className=\"text-xs\">Ask me about your drawing or anything else!</p>\n          </div>\n        ) : (\n          aiMessages.map((msg) => (\n            <div key={msg.id} className=\"space-y-2\">\n              <div className=\"prose prose-sm max-w-none dark:prose-invert\">\n                <ReactMarkdown\n                  components={{\n                    code: ({ node, inline, className, children, ...props }) => {\n                      if (inline) {\n                        return (\n                          <code className=\"bg-secondary px-1 py-0.5 rounded text-sm\" {...props}>\n                            {children}\n                          </code>\n                        )\n                      }\n                      return (\n                        <pre className=\"bg-secondary p-3 rounded-lg overflow-x-auto\">\n                          <code {...props}>{children}</code>\n                        </pre>\n                      )\n                    },\n                  }}\n                >\n                  {msg.content}\n                </ReactMarkdown>\n              </div>\n              \n              <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                <span>{formatTimestamp(msg.timestamp)}</span>\n                {msg.isStreaming && (\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-1 h-1 bg-blue-500 rounded-full animate-pulse\"></div>\n                    <span>Thinking...</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"p-4 border-t border-border\">\n        <div className=\"flex gap-2\">\n          <Input\n            ref={inputRef}\n            placeholder=\"Ask AI about your drawing...\"\n            value={prompt}\n            onChange={(e) => setPrompt(e.target.value)}\n            onKeyPress={handleKeyPress}\n            disabled={isLoading}\n            className=\"flex-1\"\n          />\n          <Button\n            onClick={handleSendPrompt}\n            disabled={!prompt.trim() || isLoading}\n            size=\"icon\"\n            className=\"shrink-0\"\n          >\n            <Send className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAYO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,eAAe,EACf,UAAU,EACV,QAAQ,EACT,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,SAAS,OAAO,EAAE;IACpB,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO,IAAI,MAAM,WAAW;QAEjC,MAAM,aAAa,OAAO,IAAI;QAC9B,UAAU;QACV,aAAa;QAEb,mBAAmB;QACnB,MAAM,cAAyB;YAC7B,IAAI,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD;YACjB,SAAS,CAAC,SAAS,EAAE,YAAY;YACjC,WAAW,KAAK,GAAG;YACnB,aAAa;QACf;QACA,aAAa;QAEb,8BAA8B;QAC9B,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD;QACjC,MAAM,YAAuB;YAC3B,IAAI;YACJ,SAAS;YACT,WAAW,KAAK,GAAG;YACnB,aAAa;YACb,eAAe,aAAa,KAAK,SAAS,CAAC,cAAc;QAC3D;QACA,aAAa;QAEb,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,eAAe;gBACjB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,SAAS,IAAI,EAAE;YAC9B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,qBAAqB;YAEzB,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBAEzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,IAAI,cAAc,MAAM,CAAC;gBACvC,MAAM,QAAQ,MAAM,KAAK,CAAC;gBAE1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,IAAI;4BACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;4BACnC,IAAI,KAAK,OAAO,EAAE;gCAChB,sBAAsB,KAAK,OAAO;gCAClC,gBAAgB,aAAa,oBAAoB;4BACnD;wBACF,EAAE,OAAO,GAAG;wBACV,8CAA8C;wBAChD;oBACF;gBACF;YACF;YAEA,mBAAmB;YACnB,gBAAgB,aAAa,oBAAoB;QAEnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,gBACE,aACA,sDACA;QAEJ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,SAAS;gCAAE,UAAU;4BAAM;wBAC1C,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;;oBACZ,WAAW,MAAM,KAAK,kBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;+BAGzB,WAAW,GAAG,CAAC,CAAC,oBACd,8OAAC;4BAAiB,WAAU;;8CAC1B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;wCACZ,YAAY;4CACV,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;gDACpD,IAAI,QAAQ;oDACV,qBACE,8OAAC;wDAAK,WAAU;wDAA4C,GAAG,KAAK;kEACjE;;;;;;gDAGP;gDACA,qBACE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAM,GAAG,KAAK;kEAAG;;;;;;;;;;;4CAGxB;wCACF;kDAEC,IAAI,OAAO;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,SAAS;;;;;;wCACnC,IAAI,WAAW,kBACd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;2BA7BJ,IAAI,EAAE;;;;;kCAoCpB,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,KAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4BACzC,YAAY;4BACZ,UAAU;4BACV,WAAU;;;;;;sCAEZ,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,OAAO,IAAI,MAAM;4BAC5B,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/canvas/UserCursors.tsx"], "sourcesContent": ["'use client'\n\nimport { useAppStore } from '@/stores/useAppStore'\nimport { getInitials } from '@/lib/utils'\n\nexport function UserCursors() {\n  const { users, currentUser } = useAppStore()\n\n  return (\n    <div className=\"absolute inset-0 pointer-events-none z-50\">\n      {Array.from(users.values())\n        .filter(user => user.id !== currentUser?.id && user.cursor)\n        .map(user => (\n          <div\n            key={user.id}\n            className=\"absolute transition-all duration-100 ease-out\"\n            style={{\n              left: user.cursor!.x,\n              top: user.cursor!.y,\n              transform: 'translate(-2px, -2px)',\n            }}\n          >\n            {/* Cursor */}\n            <svg\n              width=\"24\"\n              height=\"24\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              className=\"drop-shadow-sm\"\n            >\n              <path\n                d=\"M5.65376 12.3673H5.46026L5.31717 12.4976L0.500002 16.8829L0.500002 1.19841L11.7841 12.3673H5.65376Z\"\n                fill={user.color}\n                stroke=\"white\"\n                strokeWidth=\"1\"\n              />\n            </svg>\n            \n            {/* User label */}\n            <div\n              className=\"absolute top-5 left-2 px-2 py-1 rounded text-xs font-medium text-white shadow-lg\"\n              style={{ backgroundColor: user.color }}\n            >\n              {user.name}\n            </div>\n          </div>\n        ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEzC,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC,MAAM,MAAM,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa,MAAM,KAAK,MAAM,EACzD,GAAG,CAAC,CAAA,qBACH,8OAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,MAAM,KAAK,MAAM,CAAE,CAAC;oBACpB,KAAK,KAAK,MAAM,CAAE,CAAC;oBACnB,WAAW;gBACb;;kCAGA,8OAAC;wBACC,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC;4BACC,GAAE;4BACF,MAAM,KAAK,KAAK;4BAChB,QAAO;4BACP,aAAY;;;;;;;;;;;kCAKhB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,KAAK,KAAK;wBAAC;kCAEpC,KAAK,IAAI;;;;;;;eA7BP,KAAK,EAAE;;;;;;;;;;AAmCxB", "debugId": null}}, {"offset": {"line": 1633, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/ui/ConnectionStatus.tsx"], "sourcesContent": ["'use client'\n\ninterface ConnectionStatusProps {\n  isConnected: boolean\n}\n\nexport function ConnectionStatus({ isConnected }: ConnectionStatusProps) {\n  return (\n    <div className=\"floating-panel px-3 py-2 flex items-center gap-2\">\n      <div\n        className={`w-2 h-2 rounded-full ${\n          isConnected \n            ? 'bg-green-500 animate-pulse-soft' \n            : 'bg-red-500'\n        }`}\n      />\n      <span className=\"text-xs font-medium\">\n        {isConnected ? 'Connected' : 'Disconnected'}\n      </span>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAMO,SAAS,iBAAiB,EAAE,WAAW,EAAyB;IACrE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC,qBAAqB,EAC/B,cACI,oCACA,cACJ;;;;;;0BAEJ,8OAAC;gBAAK,WAAU;0BACb,cAAc,cAAc;;;;;;;;;;;;AAIrC", "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/ui/ShareButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { But<PERSON> } from './Button'\nimport { Share2, Co<PERSON>, Check } from 'lucide-react'\nimport { copyToClipboard } from '@/lib/utils'\n\ninterface ShareButtonProps {\n  roomId: string\n}\n\nexport function ShareButton({ roomId }: ShareButtonProps) {\n  const [copied, setCopied] = useState(false)\n\n  const handleShare = async () => {\n    const url = `${window.location.origin}/room/${roomId}`\n    const success = await copyToClipboard(url)\n    \n    if (success) {\n      setCopied(true)\n      setTimeout(() => setCopied(false), 2000)\n    }\n  }\n\n  return (\n    <Button\n      variant=\"outline\"\n      size=\"sm\"\n      onClick={handleShare}\n      className=\"gap-2 bg-background/80 backdrop-blur-sm\"\n    >\n      {copied ? (\n        <>\n          <Check className=\"h-4 w-4 text-green-600\" />\n          <span>Copied!</span>\n        </>\n      ) : (\n        <>\n          <Share2 className=\"h-4 w-4\" />\n          <span>Share</span>\n        </>\n      )}\n    </Button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAWO,SAAS,YAAY,EAAE,MAAM,EAAoB;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,cAAc;QAClB,MAAM,MAAM,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ;QACtD,MAAM,UAAU,MAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;QAEtC,IAAI,SAAS;YACX,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;kBAET,uBACC;;8BACE,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,8OAAC;8BAAK;;;;;;;yCAGR;;8BACE,8OAAC,0MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,8OAAC;8BAAK;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/app/room/%5BroomId%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'\nimport dynamic from 'next/dynamic'\nimport { useAppStore } from '@/stores/useAppStore'\nimport { useWebSocket } from '@/hooks/useWebSocket'\nimport { FloatingToolbar } from '@/components/ui/FloatingToolbar'\nimport { ChatPanel } from '@/components/chat/ChatPanel'\nimport { AIPanel } from '@/components/ai/AIPanel'\nimport { UserCursors } from '@/components/canvas/UserCursors'\nimport { ConnectionStatus } from '@/components/ui/ConnectionStatus'\nimport { ShareButton } from '@/components/ui/ShareButton'\n\n// Dynamically import WhiteboardCanvas to avoid SSR issues with Excalidraw\nconst WhiteboardCanvas = dynamic(\n  () => import('@/components/canvas/WhiteboardCanvas').then(mod => ({ default: mod.WhiteboardCanvas })),\n  {\n    ssr: false,\n    loading: () => (\n      <div className=\"w-full h-full flex items-center justify-center bg-muted/20\">\n        <div className=\"text-center\">\n          <div className=\"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2\"></div>\n          <p className=\"text-sm text-muted-foreground\">Loading whiteboard...</p>\n        </div>\n      </div>\n    )\n  }\n)\n\nexport default function RoomPage() {\n  const params = useParams()\n  const router = useRouter()\n  const roomId = params.roomId as string\n  \n  const {\n    currentUser,\n    setRoomId,\n    ui,\n    updateUI,\n  } = useAppStore()\n\n  const { isConnected } = useWebSocket(roomId)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    if (!currentUser) {\n      router.push('/')\n      return\n    }\n\n    setRoomId(roomId)\n    setIsLoading(false)\n  }, [currentUser, roomId, router, setRoomId])\n\n  if (isLoading) {\n    return (\n      <div className=\"h-screen w-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\n          <p className=\"text-muted-foreground\">Loading whiteboard...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!currentUser) {\n    return null\n  }\n\n  return (\n    <div className=\"h-screen w-screen relative overflow-hidden bg-background\">\n      {/* Main Canvas */}\n      <WhiteboardCanvas />\n      \n      {/* User Cursors */}\n      <UserCursors />\n      \n      {/* Floating UI Elements */}\n      <div className=\"absolute inset-0 pointer-events-none\">\n        {/* Top Bar */}\n        <div className=\"absolute top-4 left-4 right-4 flex items-center justify-between pointer-events-auto\">\n          <div className=\"flex items-center gap-3\">\n            <ConnectionStatus isConnected={isConnected} />\n            <div className=\"floating-panel px-3 py-2\">\n              <span className=\"text-sm font-medium\">Room: {roomId}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <ShareButton roomId={roomId} />\n            <FloatingToolbar />\n          </div>\n        </div>\n\n        {/* Chat Panel */}\n        {ui.isChatOpen && (\n          <div className=\"absolute top-16 right-4 bottom-4 w-80 pointer-events-auto\">\n            <ChatPanel />\n          </div>\n        )}\n\n        {/* AI Panel */}\n        {ui.isAIOpen && (\n          <div className=\"absolute top-16 right-4 bottom-4 w-80 pointer-events-auto\">\n            <AIPanel />\n          </div>\n        )}\n\n        {/* Bottom Controls */}\n        <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 pointer-events-auto\">\n          <div className=\"floating-panel px-4 py-2 flex items-center gap-3\">\n            <button\n              onClick={() => updateUI({ isChatOpen: !ui.isChatOpen, isAIOpen: false })}\n              className={`px-3 py-1 rounded text-sm transition-colors ${\n                ui.isChatOpen \n                  ? 'bg-primary text-primary-foreground' \n                  : 'hover:bg-accent hover:text-accent-foreground'\n              }`}\n            >\n              💬 Chat\n            </button>\n            \n            <button\n              onClick={() => updateUI({ isAIOpen: !ui.isAIOpen, isChatOpen: false })}\n              className={`px-3 py-1 rounded text-sm transition-colors ${\n                ui.isAIOpen \n                  ? 'bg-primary text-primary-foreground' \n                  : 'hover:bg-accent hover:text-accent-foreground'\n              }`}\n            >\n              🤖 AI Assistant\n            </button>\n            \n            <div className=\"w-px h-4 bg-border\"></div>\n            \n            <button\n              onClick={() => updateUI({ theme: ui.theme === 'light' ? 'dark' : 'light' })}\n              className=\"px-3 py-1 rounded text-sm hover:bg-accent hover:text-accent-foreground transition-colors\"\n            >\n              {ui.theme === 'light' ? '🌙' : '☀️'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAZA;;;;;;;;;;;;;AAcA,0EAA0E;AAC1E,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAG3B,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;;AAOxC,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,MAAM;IAE5B,MAAM,EACJ,WAAW,EACX,SAAS,EACT,EAAE,EACF,QAAQ,EACT,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;YAChB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,UAAU;QACV,aAAa;IACf,GAAG;QAAC;QAAa;QAAQ;QAAQ;KAAU;IAE3C,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;;;;0BAGD,8OAAC,2IAAA,CAAA,cAAW;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4IAAA,CAAA,mBAAgB;wCAAC,aAAa;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDAAsB;gDAAO;;;;;;;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uIAAA,CAAA,cAAW;wCAAC,QAAQ;;;;;;kDACrB,8OAAC,2IAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;oBAKnB,GAAG,UAAU,kBACZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uIAAA,CAAA,YAAS;;;;;;;;;;oBAKb,GAAG,QAAQ,kBACV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;;;;;;;;;;kCAKZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,SAAS;4CAAE,YAAY,CAAC,GAAG,UAAU;4CAAE,UAAU;wCAAM;oCACtE,WAAW,CAAC,4CAA4C,EACtD,GAAG,UAAU,GACT,uCACA,gDACJ;8CACH;;;;;;8CAID,8OAAC;oCACC,SAAS,IAAM,SAAS;4CAAE,UAAU,CAAC,GAAG,QAAQ;4CAAE,YAAY;wCAAM;oCACpE,WAAW,CAAC,4CAA4C,EACtD,GAAG,QAAQ,GACP,uCACA,gDACJ;8CACH;;;;;;8CAID,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCACC,SAAS,IAAM,SAAS;4CAAE,OAAO,GAAG,KAAK,KAAK,UAAU,SAAS;wCAAQ;oCACzE,WAAU;8CAET,GAAG,KAAK,KAAK,UAAU,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}]}