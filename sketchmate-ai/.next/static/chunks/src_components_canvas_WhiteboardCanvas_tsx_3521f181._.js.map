{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Prototype%203%20_%20MAJOR%20PROJECT%20NOT%20STABLE%20%21/sketchmate-ai/src/components/canvas/WhiteboardCanvas.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\n\n// Dynamically import the Excalidraw wrapper to avoid SSR issues\nconst ExcalidrawWrapper = dynamic(\n  () => import('./ExcalidrawWrapper').then(mod => ({ default: mod.ExcalidrawWrapper })),\n  {\n    ssr: false,\n    loading: () => (\n      <div className=\"w-full h-full flex items-center justify-center bg-muted/20\">\n        <div className=\"text-center\">\n          <div className=\"w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2\"></div>\n          <p className=\"text-sm text-muted-foreground\">Loading whiteboard...</p>\n        </div>\n      </div>\n    )\n  }\n)\n\nexport function WhiteboardCanvas() {\n  return (\n    <div className=\"w-full h-full\">\n      <ExcalidrawWrapper />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;;AAIA,gEAAgE;AAChE,MAAM,oBAAoB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAC9B,IAAM;;;;SAA8B,IAAI,CAAC,CAAA,MAAO,CAAC;YAAE,SAAS,IAAI,iBAAiB;QAAC,CAAC;;;;;;IAEjF,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;;KARjD;AAeC,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;;;;;;;;;;AAGP;MANgB", "debugId": null}}]}