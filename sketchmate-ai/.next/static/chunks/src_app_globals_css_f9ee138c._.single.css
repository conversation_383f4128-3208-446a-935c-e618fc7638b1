/* [project]/src/app/globals.css [app-client] (css) */
@tailwind base;

@tailwind components;

@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: .5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  @apply border-border;

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  @apply bg-background text-foreground;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: none;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / .3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / .5);
}

.excalidraw {
  --color-primary: hsl(var(--primary));
  --color-primary-darker: hsl(var(--primary));
  --color-primary-darkest: hsl(var(--primary));
  --color-surface-low: hsl(var(--background));
  --color-surface-lowest: hsl(var(--card));
}

.excalidraw .App-menu_top {
  background: hsl(var(--background) / .8) !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: var(--radius) !important;
}

.excalidraw .App-toolbar {
  background: hsl(var(--background) / .9) !important;
  backdrop-filter: blur(12px) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: var(--radius) !important;
  box-shadow: 0 4px 6px -1px #0000001a !important;
}

@apply bg-background / 90 backdrop-blur-md border border-border rounded-lg shadow-lg;

@apply transition-all duration-200 ease-in-out;

@apply shadow-xl;

@apply opacity-0 scale-95 translate-y-1;

.dropdown-enter-active {
  transition: all .15s ease-out;
}

@apply opacity-100 scale-100 translate-y-0;

@apply opacity-100 scale-100 translate-y-0;

.dropdown-exit-active {
  transition: all .1s ease-in;
}

@apply opacity-0 scale-95 translate-y-1;

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/