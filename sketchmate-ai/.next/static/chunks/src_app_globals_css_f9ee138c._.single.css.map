{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    --background: 0 0% 100%;\n    --foreground: 222.2 84% 4.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 222.2 84% 4.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 222.2 84% 4.9%;\n    --primary: 221.2 83.2% 53.3%;\n    --primary-foreground: 210 40% 98%;\n    --secondary: 210 40% 96%;\n    --secondary-foreground: 222.2 84% 4.9%;\n    --muted: 210 40% 96%;\n    --muted-foreground: 215.4 16.3% 46.9%;\n    --accent: 210 40% 96%;\n    --accent-foreground: 222.2 84% 4.9%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 214.3 31.8% 91.4%;\n    --input: 214.3 31.8% 91.4%;\n    --ring: 221.2 83.2% 53.3%;\n    --chart-1: 12 76% 61%;\n    --chart-2: 173 58% 39%;\n    --chart-3: 197 37% 24%;\n    --chart-4: 43 74% 66%;\n    --chart-5: 27 87% 67%;\n    --radius: 0.5rem;\n  }\n\n  .dark {\n    --background: 222.2 84% 4.9%;\n    --foreground: 210 40% 98%;\n    --card: 222.2 84% 4.9%;\n    --card-foreground: 210 40% 98%;\n    --popover: 222.2 84% 4.9%;\n    --popover-foreground: 210 40% 98%;\n    --primary: 217.2 91.2% 59.8%;\n    --primary-foreground: 222.2 84% 4.9%;\n    --secondary: 217.2 32.6% 17.5%;\n    --secondary-foreground: 210 40% 98%;\n    --muted: 217.2 32.6% 17.5%;\n    --muted-foreground: 215 20.2% 65.1%;\n    --accent: 217.2 32.6% 17.5%;\n    --accent-foreground: 210 40% 98%;\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 217.2 32.6% 17.5%;\n    --input: 217.2 32.6% 17.5%;\n    --ring: 224.3 76.3% 94.1%;\n    --chart-1: 220 70% 50%;\n    --chart-2: 160 60% 45%;\n    --chart-3: 30 80% 55%;\n    --chart-4: 280 65% 60%;\n    --chart-5: 340 75% 55%;\n  }\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n  body {\n    @apply bg-background text-foreground;\n    font-feature-settings: \"rlig\" 1, \"calt\" 1;\n  }\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n::-webkit-scrollbar-thumb {\n  background: hsl(var(--muted-foreground) / 0.3);\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: hsl(var(--muted-foreground) / 0.5);\n}\n\n/* Excalidraw overrides for seamless integration */\n.excalidraw {\n  --color-primary: hsl(var(--primary));\n  --color-primary-darker: hsl(var(--primary));\n  --color-primary-darkest: hsl(var(--primary));\n  --color-surface-low: hsl(var(--background));\n  --color-surface-lowest: hsl(var(--card));\n}\n\n.excalidraw .App-menu_top {\n  background: hsl(var(--background) / 0.8) !important;\n  backdrop-filter: blur(8px) !important;\n  border: 1px solid hsl(var(--border)) !important;\n  border-radius: var(--radius) !important;\n}\n\n.excalidraw .App-toolbar {\n  background: hsl(var(--background) / 0.9) !important;\n  backdrop-filter: blur(12px) !important;\n  border: 1px solid hsl(var(--border)) !important;\n  border-radius: var(--radius) !important;\n  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1) !important;\n}\n\n/* Floating panels */\n.floating-panel {\n  @apply bg-background/90 backdrop-blur-md border border-border rounded-lg shadow-lg;\n  @apply transition-all duration-200 ease-in-out;\n}\n\n.floating-panel:hover {\n  @apply shadow-xl;\n}\n\n/* Dropdown animations */\n.dropdown-enter {\n  @apply opacity-0 scale-95 translate-y-1;\n}\n\n.dropdown-enter-active {\n  @apply opacity-100 scale-100 translate-y-0;\n  transition: all 150ms ease-out;\n}\n\n.dropdown-exit {\n  @apply opacity-100 scale-100 translate-y-0;\n}\n\n.dropdown-exit-active {\n  @apply opacity-0 scale-95 translate-y-1;\n  transition: all 100ms ease-in;\n}\n"], "names": [], "mappings": "AAAA;;AACA;;AACA;;AAEA;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BE;;EAEF;;;;EACE;;;AAMJ;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;AAUE;;AACA;;AAIA;;AAKA;;AAGF;;;;AACE;;AAKA;;AAGF;;;;AACE"}}]}