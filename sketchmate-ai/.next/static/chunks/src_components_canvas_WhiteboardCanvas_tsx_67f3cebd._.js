(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/canvas/WhiteboardCanvas.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_components_canvas_WhiteboardCanvas_tsx_3521f181._.js",
  "static/chunks/src_components_canvas_WhiteboardCanvas_tsx_4b49d558._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/canvas/WhiteboardCanvas.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);