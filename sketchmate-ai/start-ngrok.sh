#!/bin/bash

# FluxNotebook-AI ngrok Deployment Script
# This script starts the application and exposes it via ngrok for external access

echo "🎨 Starting FluxNotebook-AI with ngrok..."
echo "========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti :$port)
    if [ ! -z "$pid" ]; then
        echo -e "${YELLOW}Killing existing process on port $port (PID: $pid)${NC}"
        kill -9 $pid 2>/dev/null
        sleep 2
    fi
}

# Check if ngrok is installed
if ! command_exists ngrok; then
    echo -e "${RED}❌ ngrok is not installed!${NC}"
    echo -e "${BLUE}Please install ngrok from: https://ngrok.com/download${NC}"
    echo -e "${BLUE}Or install via Homebrew: brew install ngrok${NC}"
    exit 1
fi

# Check if Node.js is installed
if ! command_exists node; then
    echo -e "${RED}❌ Node.js is not installed!${NC}"
    echo -e "${BLUE}Please install Node.js from: https://nodejs.org/${NC}"
    exit 1
fi

# Check if npm is installed
if ! command_exists npm; then
    echo -e "${RED}❌ npm is not installed!${NC}"
    echo -e "${BLUE}Please install npm (usually comes with Node.js)${NC}"
    exit 1
fi

# Set ngrok authtoken
echo -e "${BLUE}🔑 Setting up ngrok authtoken...${NC}"
ngrok authtoken *************************************************

# Kill any existing ngrok processes (for free version compatibility)
echo -e "${BLUE}🧹 Cleaning up existing ngrok sessions...${NC}"
pkill -f ngrok 2>/dev/null || true
sleep 2

# Kill any existing processes on our ports
echo -e "${BLUE}🧹 Cleaning up existing processes...${NC}"
kill_port 3000
kill_port 3001

# Additional cleanup for ngrok free version
echo -e "${BLUE}🔄 Ensuring no conflicting ngrok sessions...${NC}"
sleep 3

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    npm install --legacy-peer-deps
fi

# Start the WebSocket server in background
echo -e "${BLUE}🔌 Starting WebSocket server on port 3001...${NC}"
node server/websocket.js &
WEBSOCKET_PID=$!

# Wait a moment for WebSocket server to start
sleep 3

# Check if WebSocket server started successfully
if ! port_in_use 3001; then
    echo -e "${RED}❌ Failed to start WebSocket server on port 3001${NC}"
    exit 1
fi

echo -e "${GREEN}✅ WebSocket server started successfully (PID: $WEBSOCKET_PID)${NC}"

# Start the Next.js development server in background
echo -e "${BLUE}🚀 Starting Next.js development server on port 3000...${NC}"
npm run dev &
NEXTJS_PID=$!

# Wait for Next.js server to start
echo -e "${YELLOW}⏳ Waiting for Next.js server to start...${NC}"
sleep 10

# Check if Next.js server started successfully
if ! port_in_use 3000; then
    echo -e "${RED}❌ Failed to start Next.js server on port 3000${NC}"
    kill $WEBSOCKET_PID 2>/dev/null
    exit 1
fi

echo -e "${GREEN}✅ Next.js server started successfully (PID: $NEXTJS_PID)${NC}"

# Start ngrok tunnel for the main application
echo -e "${BLUE}🌐 Starting ngrok tunnel for port 3000...${NC}"
ngrok http 3000 --log=stdout &
NGROK_PID=$!

# Wait a moment for ngrok to start
sleep 5

echo ""
echo -e "${GREEN}🎉 FluxNotebook-AI is now running!${NC}"
echo "========================================="
echo -e "${BLUE}📱 Local URLs:${NC}"
echo -e "   • Next.js App: ${GREEN}http://localhost:3000${NC}"
echo -e "   • WebSocket:   ${GREEN}http://localhost:3001${NC}"
echo ""
echo -e "${BLUE}🌍 Public URL:${NC}"
echo -e "   • Check ngrok dashboard: ${GREEN}http://localhost:4040${NC}"
echo -e "   • Or look for the public URL in the ngrok output above${NC}"
echo ""
echo -e "${YELLOW}📋 Share the ngrok public URL with others to collaborate!${NC}"
echo ""
echo -e "${BLUE}🛑 To stop all services, press Ctrl+C${NC}"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Shutting down services...${NC}"
    kill $NGROK_PID 2>/dev/null
    kill $NEXTJS_PID 2>/dev/null
    kill $WEBSOCKET_PID 2>/dev/null
    kill_port 3000
    kill_port 3001
    echo -e "${GREEN}✅ All services stopped${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Keep the script running
wait
