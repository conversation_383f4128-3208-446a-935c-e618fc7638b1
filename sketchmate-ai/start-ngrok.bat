@echo off
setlocal enabledelayedexpansion

REM SketchMate-AI ngrok Deployment Script for Windows
REM This script starts the application and exposes it via ngrok for external access

echo 🎨 Starting SketchMate-AI with ngrok...
echo =========================================

REM Function to check if a command exists
where ngrok >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ngrok is not installed!
    echo Please install ngrok from: https://ngrok.com/download
    echo Or install via Chocolatey: choco install ngrok
    pause
    exit /b 1
)

where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed!
    echo Please install npm (usually comes with Node.js)
    pause
    exit /b 1
)

REM Set ngrok authtoken
echo 🔑 Setting up ngrok authtoken...
ngrok authtoken *************************************************

REM Kill any existing processes on our ports
echo 🧹 Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3001" ^| find "LISTENING"') do taskkill /f /pid %%a >nul 2>&1

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
)

REM Start the WebSocket server in background
echo 🔌 Starting WebSocket server on port 3001...
start /b node server/websocket.js

REM Wait a moment for WebSocket server to start
timeout /t 3 /nobreak >nul

REM Start the Next.js development server in background
echo 🚀 Starting Next.js development server on port 3000...
start /b npm run dev

REM Wait for Next.js server to start
echo ⏳ Waiting for Next.js server to start...
timeout /t 10 /nobreak >nul

REM Start ngrok tunnel for the main application
echo 🌐 Starting ngrok tunnel for port 3000...
start /b ngrok http 3000

REM Wait a moment for ngrok to start
timeout /t 5 /nobreak >nul

echo.
echo 🎉 SketchMate-AI is now running!
echo =========================================
echo 📱 Local URLs:
echo    • Next.js App: http://localhost:3000
echo    • WebSocket:   http://localhost:3001
echo.
echo 🌍 Public URL:
echo    • Check ngrok dashboard: http://localhost:4040
echo    • Or look for the ngrok window that opened
echo.
echo 📋 Share the ngrok public URL with others to collaborate!
echo.
echo 🛑 To stop all services, close this window or press Ctrl+C
echo.

REM Keep the script running
pause
