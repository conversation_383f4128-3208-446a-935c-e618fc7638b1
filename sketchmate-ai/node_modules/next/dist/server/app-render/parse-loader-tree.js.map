{"version": 3, "sources": ["../../../src/server/app-render/parse-loader-tree.ts"], "sourcesContent": ["import { DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { LoaderTree } from '../lib/app-dir-module'\n\nexport function parseLoaderTree(tree: LoaderTree) {\n  const [segment, parallelRoutes, modules] = tree\n  const { layout } = modules\n  let { page } = modules\n  // a __DEFAULT__ segment means that this route didn't match any of the\n  // segments in the route, so we should use the default page\n  page = segment === DEFAULT_SEGMENT_KEY ? modules.defaultPage : page\n\n  const layoutOrPagePath = layout?.[1] || page?.[1]\n\n  return {\n    page,\n    segment,\n    modules,\n    layoutOrPagePath,\n    parallelRoutes,\n  }\n}\n"], "names": ["parseLoaderTree", "tree", "segment", "parallelRoutes", "modules", "layout", "page", "DEFAULT_SEGMENT_KEY", "defaultPage", "layoutOrPagePath"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;yBAHoB;AAG7B,SAASA,gBAAgBC,IAAgB;IAC9C,MAAM,CAACC,SAASC,gBAAgBC,QAAQ,GAAGH;IAC3C,MAAM,EAAEI,MAAM,EAAE,GAAGD;IACnB,IAAI,EAAEE,IAAI,EAAE,GAAGF;IACf,sEAAsE;IACtE,2DAA2D;IAC3DE,OAAOJ,YAAYK,4BAAmB,GAAGH,QAAQI,WAAW,GAAGF;IAE/D,MAAMG,mBAAmBJ,CAAAA,0BAAAA,MAAQ,CAAC,EAAE,MAAIC,wBAAAA,IAAM,CAAC,EAAE;IAEjD,OAAO;QACLA;QACAJ;QACAE;QACAK;QACAN;IACF;AACF"}