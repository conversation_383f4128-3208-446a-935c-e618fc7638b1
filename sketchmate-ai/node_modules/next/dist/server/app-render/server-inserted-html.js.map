{"version": 3, "sources": ["../../../src/server/app-render/server-inserted-html.tsx"], "sourcesContent": ["// Provider for the `useServerInsertedHTML` API to register callbacks to insert\n// elements into the HTML stream.\n\nimport React, { type JSX } from 'react'\nimport { ServerInsertedHTMLContext } from '../../shared/lib/server-inserted-html.shared-runtime'\n\nexport function createServerInsertedHTML() {\n  const serverInsertedHTMLCallbacks: (() => React.ReactNode)[] = []\n  const addInsertedHtml = (handler: () => React.ReactNode) => {\n    serverInsertedHTMLCallbacks.push(handler)\n  }\n\n  return {\n    ServerInsertedHTMLProvider({ children }: { children: JSX.Element }) {\n      return (\n        <ServerInsertedHTMLContext.Provider value={addInsertedHtml}>\n          {children}\n        </ServerInsertedHTMLContext.Provider>\n      )\n    },\n    renderServerInsertedHTML() {\n      return serverInsertedHTMLCallbacks.map((callback, index) => (\n        <React.Fragment key={'__next_server_inserted__' + index}>\n          {callback()}\n        </React.Fragment>\n      ))\n    },\n  }\n}\n"], "names": ["createServerInsertedHTML", "serverInsertedHTMLCallbacks", "addInsertedHtml", "handler", "push", "ServerInsertedHTMLProvider", "children", "ServerInsertedHTMLContext", "Provider", "value", "renderServerInsertedHTML", "map", "callback", "index", "React", "Fragment"], "mappings": "AAAA,+EAA+E;AAC/E,iCAAiC;;;;;+BAKjBA;;;eAAAA;;;;8DAHgB;iDACU;;;;;;AAEnC,SAASA;IACd,MAAMC,8BAAyD,EAAE;IACjE,MAAMC,kBAAkB,CAACC;QACvBF,4BAA4BG,IAAI,CAACD;IACnC;IAEA,OAAO;QACLE,4BAA2B,EAAEC,QAAQ,EAA6B;YAChE,qBACE,qBAACC,0DAAyB,CAACC,QAAQ;gBAACC,OAAOP;0BACxCI;;QAGP;QACAI;YACE,OAAOT,4BAA4BU,GAAG,CAAC,CAACC,UAAUC,sBAChD,qBAACC,cAAK,CAACC,QAAQ;8BACZH;mBADkB,6BAA6BC;QAItD;IACF;AACF"}