{"version": 3, "sources": ["../../../src/server/app-render/strip-flight-headers.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'node:http'\n\nimport { FLIGHT_HEADERS } from '../../client/components/app-router-headers'\n\n/**\n * Removes the flight headers from the request.\n *\n * @param req the request to strip the headers from\n */\nexport function stripFlightHeaders(headers: IncomingHttpHeaders) {\n  for (const header of FLIGHT_HEADERS) {\n    delete headers[header.toLowerCase()]\n  }\n}\n"], "names": ["stripFlightHeaders", "headers", "header", "FLIGHT_HEADERS", "toLowerCase"], "mappings": ";;;;+BASgBA;;;eAAAA;;;kCAPe;AAOxB,SAASA,mBAAmBC,OAA4B;IAC7D,KAAK,MAAMC,UAAUC,gCAAc,CAAE;QACnC,OAAOF,OAAO,CAACC,OAAOE,WAAW,GAAG;IACtC;AACF"}