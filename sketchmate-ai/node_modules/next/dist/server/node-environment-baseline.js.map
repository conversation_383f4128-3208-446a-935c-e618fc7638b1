{"version": 3, "sources": ["../../src/server/node-environment-baseline.ts"], "sourcesContent": ["// This file should be imported before any others. It sets up the environment\n// for later imports to work properly.\n\n// expose AsyncLocalStorage on global for react usage if it isn't already provided by the environment\nif (typeof (globalThis as any).AsyncLocalStorage !== 'function') {\n  const { AsyncLocalStorage } = require('async_hooks')\n  ;(globalThis as any).AsyncLocalStorage = AsyncLocalStorage\n}\n\nif (typeof (globalThis as any).WebSocket !== 'function') {\n  Object.defineProperty(globalThis, 'WebSocket', {\n    configurable: true,\n    get() {\n      return require('next/dist/compiled/ws').WebSocket\n    },\n    set(value) {\n      Object.defineProperty(globalThis, 'WebSocket', {\n        configurable: true,\n        writable: true,\n        value,\n      })\n    },\n  })\n}\n"], "names": ["globalThis", "AsyncLocalStorage", "require", "WebSocket", "Object", "defineProperty", "configurable", "get", "set", "value", "writable"], "mappings": "AAAA,6EAA6E;AAC7E,sCAAsC;AAEtC,qGAAqG;;AACrG,IAAI,OAAO,AAACA,WAAmBC,iBAAiB,KAAK,YAAY;IAC/D,MAAM,EAAEA,iBAAiB,EAAE,GAAGC,QAAQ;IACpCF,WAAmBC,iBAAiB,GAAGA;AAC3C;AAEA,IAAI,OAAO,AAACD,WAAmBG,SAAS,KAAK,YAAY;IACvDC,OAAOC,cAAc,CAACL,YAAY,aAAa;QAC7CM,cAAc;QACdC;YACE,OAAOL,QAAQ,yBAAyBC,SAAS;QACnD;QACAK,KAAIC,KAAK;YACPL,OAAOC,cAAc,CAACL,YAAY,aAAa;gBAC7CM,cAAc;gBACdI,UAAU;gBACVD;YACF;QACF;IACF;AACF"}