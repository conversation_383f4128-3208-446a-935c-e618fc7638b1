{"version": 3, "sources": ["../../../../src/server/route-modules/app-page/module.render.ts"], "sourcesContent": ["import type { AppPageRender } from '../../app-render/app-render'\n\nexport const lazyRenderAppPage: AppPageRender = (...args) => {\n  if (process.env.NEXT_MINIMAL) {\n    throw new Error(\"Can't use lazyRenderAppPage in minimal mode\")\n  } else {\n    const render: AppPageRender =\n      require('./module.compiled').renderToHTMLOrFlight\n\n    return render(...args)\n  }\n}\n"], "names": ["lazyRenderAppPage", "args", "process", "env", "NEXT_MINIMAL", "Error", "render", "require", "renderToHTMLOrFlight"], "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA,oBAAmC,CAAC,GAAGC;IAClD,IAAIC,QAAQC,GAAG,CAACC,YAAY,EAAE;QAC5B,MAAM,qBAAwD,CAAxD,IAAIC,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D,OAAO;QACL,MAAMC,SACJC,QAAQ,qBAAqBC,oBAAoB;QAEnD,OAAOF,UAAUL;IACnB;AACF"}