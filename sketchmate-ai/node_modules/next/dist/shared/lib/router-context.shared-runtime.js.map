{"version": 3, "sources": ["../../../src/shared/lib/router-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n"], "names": ["RouterContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": ";;;;+BAGaA;;;eAAAA;;;;gEAHK;AAGX,MAAMA,gBAAgBC,cAAK,CAACC,aAAa,CAAoB;AAEpE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCL,cAAcM,WAAW,GAAG;AAC9B"}