cmd_Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/filter_policy.o := c++ -o Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/filter_policy.o ../deps/leveldb/leveldb-1.20/util/filter_policy.cc '-DNODE_GYP_MODULE_NAME=leveldb' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPEN<PERSON>L_NO_PINSHARED' '-DOPEN<PERSON><PERSON>_THREADS' '-DSNAPPY=1' '-DLEVELDB_PLATFORM_POSIX=1' '-DOS_MACOSX=1' -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/src -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/v8/include -I../deps/leveldb/leveldb-1.20 -I../deps/leveldb/leveldb-1.20/include -I../deps/snappy/mac -I../deps/snappy/snappy  -O3 -gdwarf-2 -mmacosx-version-min=10.15 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -Wno-sign-compare -Wno-unused-variable -Wno-unused-function -std=gnu++17 -stdlib=libc++ -fno-rtti -fno-exceptions -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/filter_policy.o.d.raw   -c
Release/obj.target/leveldb/deps/leveldb/leveldb-1.20/util/filter_policy.o: \
  ../deps/leveldb/leveldb-1.20/util/filter_policy.cc \
  ../deps/leveldb/leveldb-1.20/include/leveldb/filter_policy.h
../deps/leveldb/leveldb-1.20/util/filter_policy.cc:
../deps/leveldb/leveldb-1.20/include/leveldb/filter_policy.h:
