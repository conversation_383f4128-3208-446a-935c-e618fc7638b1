cmd_Release/obj.target/leveldown/binding.o := c++ -o Release/obj.target/leveldown/binding.o ../binding.cc '-DNODE_GYP_MODULE_NAME=leveldown' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/src -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/20.10.0/deps/v8/include -I../../napi-macros -I../deps/leveldb/leveldb-1.20/include -I../deps/leveldb/leveldb-1.20/port -I../deps/leveldb/leveldb-1.20/util -I../deps/leveldb/leveldb-1.20  -O3 -gdwarf-2 -fvisibility=hidden -mmacosx-version-min=10.15 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -fno-exceptions -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/leveldown/binding.o.d.raw   -c
Release/obj.target/leveldown/binding.o: ../binding.cc \
  ../../napi-macros/napi-macros.h \
  /Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/node_api_types.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/db.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/iterator.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/slice.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/status.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/options.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/write_batch.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/cache.h \
  ../deps/leveldb/leveldb-1.20/include/leveldb/filter_policy.h
../binding.cc:
../../napi-macros/napi-macros.h:
/Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/20.10.0/include/node/node_api_types.h:
../deps/leveldb/leveldb-1.20/include/leveldb/db.h:
../deps/leveldb/leveldb-1.20/include/leveldb/iterator.h:
../deps/leveldb/leveldb-1.20/include/leveldb/slice.h:
../deps/leveldb/leveldb-1.20/include/leveldb/status.h:
../deps/leveldb/leveldb-1.20/include/leveldb/options.h:
../deps/leveldb/leveldb-1.20/include/leveldb/write_batch.h:
../deps/leveldb/leveldb-1.20/include/leveldb/cache.h:
../deps/leveldb/leveldb-1.20/include/leveldb/filter_policy.h:
