'use client'

import { useEffect, useRef, useState } from 'react'
import { useAppStore } from '@/stores/useAppStore'
import { useWebSocket } from '@/hooks/useWebSocket'
import { throttle } from '@/lib/utils'

// This component will only render on the client side
export function ExcalidrawWrapper() {
  const excalidrawRef = useRef<any>(null)
  const [excalidrawAPI, setExcalidrawAPI] = useState<any>(null)
  const [ExcalidrawComponent, setExcalidrawComponent] = useState<any>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  
  const {
    canvasData,
    updateCanvasData,
    roomId,
    ui,
  } = useAppStore()

  const { sendMessage } = useWebSocket(roomId)

  // Load Excalidraw dynamically on the client side
  useEffect(() => {
    const loadExcalidraw = async () => {
      try {
        const excalidrawModule = await import('@excalidraw/excalidraw')
        setExcalidrawComponent(() => excalidrawModule.Excalidraw)
        setIsLoaded(true)
      } catch (error) {
        console.error('Failed to load Excalidraw:', error)
      }
    }

    loadExcalidraw()
  }, [])

  // Throttled canvas update function
  const throttledCanvasUpdate = useRef(
    throttle((elements: any, appState: any, files: any) => {
      const canvasData = {
        elements,
        appState: {
          ...appState,
          collaborators: new Map(), // Remove collaborators to avoid serialization issues
        },
        files,
      }
      
      updateCanvasData(canvasData)
      
      if (sendMessage && roomId) {
        sendMessage({
          type: 'canvas-update',
          data: canvasData,
        })
      }
    }, 100)
  ).current

  const handleChange = (elements: any, appState: any, files: any) => {
    throttledCanvasUpdate(elements, appState, files)
  }

  const handlePointerUpdate = (payload: any) => {
    // Handle cursor updates here if needed
  }

  if (!isLoaded || !ExcalidrawComponent) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-muted/20">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading whiteboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      <ExcalidrawComponent
        ref={excalidrawRef}
        initialData={canvasData}
        onChange={handleChange}
        onPointerUpdate={handlePointerUpdate}
        excalidrawAPI={(api: any) => setExcalidrawAPI(api)}
        theme={ui.theme}
        UIOptions={{
          canvasActions: {
            loadScene: false,
            saveToActiveFile: false,
            export: false,
            saveAsImage: false,
          },
        }}
      />
    </div>
  )
}
